<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interactive Web Design Styles Showcase</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YR81E31MX4"></script>
    <script>
      window.dataLayer = window.dataLayer || []
      
      function gtag() {
        dataLayer.push(arguments)
      }
      gtag('js', new Date())
      gtag('config', 'G-YR81E31MX4')
    </script>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Covered+By+Your+Grace&family=Permanent+Marker&family=Orbitron:wght@400;700;900&display=swap');
      :root {
        /* Customize these to change stripe look */
        --stripe-color: rgba(252, 255, 75, 0.85);
        /* color of the stripe */
        --dark-stripe-color: rgba(64, 60, 0, 0.95);
        /* color of the stripe */
        --stripe-width: 25px;
        /* thickness of each stripe */
        --stripe-spacing: 50px;
        /* distance from start of one stripe to the next */
        --stripe-angle: 135deg;
        /* direction of the stripes */
        --opacity: 0.8;
        /* opacity of the overlay */
        --bg-color: rgba(255, 255, 255, 0.9);
        /* background color of the overlay */
        --inverse-bg-color: rgb(0, 0, 0);
        /* background color of the inverse overlay */
        --padding: 1rem;
        /* padding of the overlay */
        --blur: 10px;
        /* blur effect */
        --rounded: 10px;
        /* border radius of the overlay */
        --accent-color: rgba(0, 122, 204, 0.9);
        /* color of the accent */
        --inverse-accent-color: rgba(164, 163, 228, 0.9);
        /* color of the inverse accent */
        --text-color: rgba(51, 51, 51, 0.9);
        /* color of the text */
        --inverse-text-color: rgba(255, 255, 255, 0.9);
        /* color of the inverse text */
      }
      
      /* Base styles */
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        line-height: 1.6;
      }
      
      /* Ensure site header stays at the very top */
      #site-header {
        position: sticky;
        z-index: 100;
        /* Ensure header stays above hero section */
      }
      
      .intro {
        display: flex;
        flex-direction: column;
        padding: 2rem;
        background-color: var(--bg-color);
        -webkit-backdrop-filter: var(--blur);
        backdrop-filter: var(--blur);
        color: var(--text-color);
        text-align: center;
        margin: 0 auto;
        max-width: 80%;
        border-radius: var(--rounded);
        box-shadow: 0 4px 8px var(--shadow-dark), 0 2px 4px var(--shadow-light);
        margin-bottom: 2rem;
      }
      
      .into h3 {
        align-items: center;
        justify-content: center;
      }
      
      .intro p {
        align-items: left;
        justify-content: center;
      }
      
      #outro {
        padding-top: 5rem;
      }
      
      .flex-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
      }
      
      .grid-item {
        flex: 1 1 300px;
        border: 1px solid var(--accent-color);
        padding: 1rem;
        margin: 0.5rem 0;
        box-sizing: border-box;
        background-color: var(--bg-color);
        color: var(--text-color);
        border-radius: var(--rounded);
      }
      
      .grid-item p {
        margin: 0.5rem;
      }
      
      /* Hero Section and Video Background */
      .hero-section {
        position: fixed;
        /* Change from relative to fixed */
        top: 0;
        /* Add top positioning */
        left: 0;
        /* Add left positioning */
        width: 100%;
        /* Simplify width */
        height: 100vh;
        /* Keep full viewport height */
        z-index: -1;
        /* Ensure it stays behind content */
      }
      
      .video-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }
      
      /* Video Container Styling */
      #traffic_videos {
        position: relative;
        width: 100%;
        height: 100%;
        background-color: black;
        /* Fallback color */
      }
      
      /* Individual Video Elements */
      [id$='_videos_1'],
      [id$='_videos_2'] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        /* Maintain aspect ratio while covering */
        transition: opacity 1s ease-in-out;
        opacity: 0;
        display: none;
      }
      
      /* Video Overlay */
      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        z-index: 1;
      }
      
      /* Content Wrapper Adjustments */
      .content-wrapper {
        position: relative;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--padding);
        z-index: 2;
        margin: 0;
        /* Remove any margins */
      }
      
      .content-wrapper h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        align-items: center;
        justify-content: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }
      
      .content-wrapper p {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        align-items: left;
        justify-content: left;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
      
      /* Header Content */
      .header-content {
        flex: 1 1 33%;
        padding: 2rem;
        border-radius: var(--rounded);
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        -webkit-backdrop-filter: var(--blur);
        backdrop-filter: var(--blur);
        opacity: var(--opacity);
        max-height: 35%;
        max-width: 35%;
      }
      
      .header-content h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
      }
      
      .header-content p {
        font-size: 1.2rem;
        line-height: 1.6;
      }
      
      .era-timeline {
        top: 0;
        left: 0;
        width: 80%;
        height: 80vh;
        margin: auto;
        padding: 2rem;
        overflow: hidden;
        /* display: none; /* Hidden by default, shown in cinematic mode */
      }
      
      article.style-card {
        padding: 1rem;
        margin-bottom: 1rem;
      }
      
      @media (max-width: 992px) {
        article.style-card {
          max-width: calc(50% - 20px);
        }
      }
      
      @media (max-width: 576px) {
        article.style-card {
          max-width: 100%;
        }
      }
      
      article.style-card:hover {
        transform: scale(1.02);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      .style-title {
        font-size: 1.25rem;
        cursor: pointer;
        color: rgba(0, 122, 204, 0.9);
        display: inline-block;
        margin-bottom: 0.25rem;
      }
      
      .style-era {
        font-style: italic;
        margin-bottom: 0.5rem;
      }
      
      .style-description {
        margin-bottom: 0.5rem;
      }
      
      .style-links a {
        margin-right: 0.5rem;
        color: #007acc;
      }
      
      /* Hide legacy extras */
      .extra-under,
      .extra-counter,
      .extra-flash-intro,
      #main-table,
      .frame-nav,
      .frame-content,
      .beta-badge,
      .card-texture,
      .grid-overlay {
        display: none !important;
      }
      
      /* Gantt chart interactive styles */
      .mermaid .task,
      .mermaid text.taskText,
      .mermaid text.sectionTitle {
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .mermaid .task:hover {
        opacity: 0.8;
        transform: translateY(-2px);
      }
      
      .mermaid {
        background-color: var(--bg-color);
        -webkit-backdrop-filter: var(--blur);
        backdrop-filter: var(--blur);
      }
      
      .mermaid text.taskText:hover {
        font-weight: bold;
      }
      
      .mermaid text.sectionTitle:hover {
        font-weight: bold;
        text-decoration: underline;
      }
      
      /* Add tooltips to Gantt elements */
      .mermaid .task,
      .mermaid text.taskText {
        position: relative;
      }
      
      /* Container for all ads */
      .ad-group {
        width: 80%;
        margin: 2rem auto;
        padding: 1rem;
        border-radius: var(--rounded);
        background-color: var(--content-bg);
        box-shadow: 0 4px 8px var(--shadow-dark), 0 2px 4px var(--shadow-light);
      }
      
      /* Each ad line */
      .ad-group .ad-line {
        margin: 0.75rem 0;
        padding: 0.75rem 1rem;
        background-color: var(--bg-color);
      
        transition: transform 0.3s ease;
        border: 1px solid var(--accent-color);
      }
      
      /* Hover effect for ad lines */
      .ad-group .ad-line:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px var(--shadow-dark);
      }
      
      /* Link styling */
      .ad-group .ad-line a {
        display: block;
        color: var(--text-color);
        font-weight: 600;
        text-decoration: none;
        transition: color 0.3s ease;
      }
      
      /* Link hover state */
      .ad-group .ad-line a:hover {
        color: var(--text-color);
        text-decoration: underline;
      }
      
      /* ===== Main Container & Layout ===== */
      #main-core {
        max-width: 1300px;
        margin: 0 auto;
      }

      /* Header Controls Styling */
      .header-controls {
        display: flex;
        justify-content: space-around;
        align-items: flex-start;
        margin-top: 2rem;
        padding: 1rem;
        background-color: var(--bg-color);
        -webkit-backdrop-filter: var(--blur);
        backdrop-filter: var(--blur);
        border-radius: var(--rounded);
        opacity: var(--opacity);
        max-width: 80%;
        margin-left: auto;
        margin-right: auto;
      }

      .control-section {
        text-align: center;
        flex: 1;
        margin: 0 1rem;
      }

      .control-section h3 {
        font-size: 1.2rem;
        font-weight: bold;
        color: var(--accent-color);
        margin-bottom: 0.5rem;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .control-section p {
        font-size: 1rem;
        color: var(--text-color);
        margin: 0;
        letter-spacing: 0.1em;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
      }

      @media (max-width: 768px) {
        .header-controls {
          flex-direction: column;
          text-align: center;
        }

        .control-section {
          margin: 0.5rem 0;
        }
      }

      
      
    </style>
    <style>
      /* Dark mode toggle styles */
      .dark-mode-toggle {
        position: fixed;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 8px 12px;
        border-radius: 20px;
        z-index: 1000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }
      
      .current-style-display {
        position: fixed;
        top: 70px;
        right: 20px;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 8px 12px;
        border-radius: 20px;
        z-index: 1000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        color: white;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }
      
      #current-theme {
        margin-left: 5px;
        color: #2196f3;
      }
      
      .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
        margin-right: 10px;
      }
      
      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
      }
      
      .slider:before {
        position: absolute;
        content: '';
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
      }
      
      input:checked + .slider {
        background-color: #2196f3;
      }
      
      input:checked + .slider:before {
        transform: translateX(26px);
      }
      
      .slider.round {
        border-radius: 34px;
      }
      
      .slider.round:before {
        border-radius: 50%;
      }
      
      .dark-mode-toggle span {
        color: white;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }
    </style>

    <!-- Dynamic theme CSS will be injected here -->
    <style id="theme-style"></style>

    <!-- Add Mermaid JS library -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>

    <!-- Add Three.js library for WebGL & 3D effects -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
  </head>

  <body>
    <div class="hero-section">
      <div class="video-background">
        <div id="color_videos">
          <video id="color_videos_1" muted playsinline webkit-playsinline></video>
          <video id="color_videos_2" muted playsinline webkit-playsinline></video>
        </div>
      </div>
    </div>
    <div class="counter">
      <div>Website visit count:</div>
      <div id="vistor-counter"></div>
    </div>
    <div id="theme-style"></div>
    <div class="dark-mode-toggle">
      <label class="switch">
        <input type="checkbox" id="dark-mode-toggle" />
        <span class="slider round"></span>
      </label>
      <span>Dark Mode</span>
    </div>
    <div class="current-style-display">
      <span>Style:</span>
      <span id="current-theme">Default</span>
    </div>
    <div id="under-construction">
      <img src="https://gifgifs.com/animations/webdesign-elements/under-construction/webpage_construction.gif" alt="Under Construction" />
    </div>
    <div class="content-wrapper">
      <div class="header-content">
        <h1>Interactive Web Design Styles Showcase</h1>
        <p>From then to now.</p>
        <p>
          <small>Click a title or press <strong>Ctrl + Shift + Space</strong> to cycle styles</small>
        </p>
      </div>

      <!-- 3D Controls Header Section -->
      <div class="header-controls">
        <div class="control-section">
          <h3>GEOMETRY</h3>
          <p>CUBE SPHERE TORUS DODECA</p>
        </div>
        <div class="control-section">
          <h3>MATERIALS</h3>
          <p>METAL GLASS NEON HOLO</p>
        </div>
        <div class="control-section">
          <h3>LIGHTING</h3>
          <p>AMBIENT DIRECTIONAL</p>
        </div>
      </div>
    </div>

    <section class="intro">
      <h3><strong>Overview</strong></h3>
      <p>
        The Interactive Web Design Styles Showcase is designed to guide you through the evolution of web design—from the playful, “Under Construction” days of GeoCities to the sleek, glass-blur panels of Glassmorphism. At its core, the page presents a chronological Gantt-style timeline of major design movements and pairs each era with a corresponding “style card” that highlights its defining characteristics, key dates, and reference links. You can explore each style by clicking its title or by pressing <strong>Ctrl + Shift + Space</strong>, which cycles through the various themes in sequence. Below the timeline, a live CSS editor displays the exact rules being applied, so you can see how each design aesthetic is constructed in real time.
      </p>
    </section>

    <nav aria-label="Jump to era" class="era-timeline">
      <h3>Web Design Evolution Timeline</h3>
      <div class="mermaid-wrapper">
        <div class="mermaid" id="era-timeline">
gantt
  title Web Design History
  dateFormat YYYY
  axisFormat %Y
  todayMarker off

  section 1990s
    Vernacular / Geocities    :vernacular, 1991, 1995
    Table-Based Design        :table-frames, 1993, 1998
    Framesets                 :framesets, 1993, 1998
    Flash-Based Design        :flash, 1996, 2008

  section 2000s
    Web 2.0 Glossy            :web2, 2004, 2013
    Skeuomorphic              :skeuomorphic, 2004, 2013
    Swiss Style               :swiss, 2000, 2025
    Minimalist                :minimalist, 2000, 2025

  section 2010s
    Responsive Design         :responsive, 2010, 2025
    Flat Design               :flat-design, 2010, 2025
    Parallax                  :parallax, 2012, 2016
    Single-Page App           :spa, 2012, 2016
    Progressive Web App       :pwa, 2015, 2025
    Cinematic                 :cinematic, 2010, 2019
    Brutalist                 :brutalist, 2015, 2025
    Neo-Brutalism             :neo-brutalism, 2015, 2025
    Corporate Memphis         :corporate-memphis, 2018, 2025
    WebGL & 3D                :webgl-3d, 2015, 2025

  section 2020s
    Dark Mode                 :dark-mode, 2020, 2025
    Neumorphism               :neumorphism, 2020, 2021
    Glassmorphism             :glassmorphism, 2020, 2021
    Retro Web                 :retro-web, 2020, 2025
    Y2K Web                   :y2k-web, 2020, 2025
    Inclusive Design          :inclusive-design, 2010, 2025
        </div>
      </div>
    </nav>

    <section class="css-editor">
      <h2>Current CSS</h2>
      <label for="css-editor">Edit CSS:</label>
      <textarea id="css-editor" editable placeholder="Enter your CSS here"></textarea>
    </section>

    <main>
      <!-- All 26 style cards -->
      <article class="style-card" data-index="1" id="vernacular">
        <h3 class="style-title">Vernacular / Geocities</h3>
        <p class="style-era">1991–1995</p>
        <p class="style-description">Amateur pages built with tiled backgrounds, animated GIFs, “Under Construction” signs, and visitor counters.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/GeoCities" target="_blank">GeoCities</a>
          <a href="https://en.wikipedia.org/wiki/Under_Construction" target="_blank">Under Construction</a>
        </p>
      </article>

      <article class="style-card" data-index="2" id="table-frames">
        <h3 class="style-title">Table-Based Design</h3>
        <p class="style-era">1993–1998</p>
        <p class="style-description">Using HTML tables to approximate multi-column layouts before CSS adoption.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/HTML_table" target="_blank">HTML Tables</a>
          <a href="https://en.wikipedia.org/wiki/CSS" target="_blank">CSS</a>
        </p>
      </article>

      <article class="style-card" data-index="3" id="framesets">
        <h3 class="style-title">Framesets</h3>
        <p class="style-era">1993–1998</p>
        <p class="style-description">Splitting the browser window into independent panes—navigation, content, etc.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/HTML_frame" target="_blank">HTML Frames</a>
          <a href="https://developer.mozilla.org/docs/Web/HTML/Element/frame" target="_blank">MDN Frames</a>
        </p>
      </article>

      <article class="style-card" data-index="4" id="flash">
        <h3 class="style-title">Flash-Based Design</h3>
        <p class="style-era">1996–2008</p>
        <p class="style-description">Rich animations, interactive menus, and full-screen intros created with Macromedia Flash.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Adobe_Flash" target="_blank">Adobe Flash</a>
          <a href="https://en.wikipedia.org/wiki/Adobe_Animate" target="_blank">Adobe Animate</a>
        </p>
      </article>

      <article class="style-card" data-index="5" id="web2">
        <h3 class="style-title">Web 2.0 Glossy</h3>
        <p class="style-era">2004–2013</p>
        <p class="style-description">Glossy buttons, drop shadows, reflections, stock-photo headers, and “beta” tags.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Web_2.0" target="_blank">Web 2.0</a>
          <a href="https://www.wired.com/" target="_blank">WIRED</a>
        </p>
      </article>

      <article class="style-card" data-index="6" id="skeuomorphic">
        <h3 class="style-title">Skeuomorphic</h3>
        <p class="style-era">2004–2013</p>
        <p class="style-description">Interfaces mimicking real-world textures (leather, metal, PostIt Notes).</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Skeuomorph" target="_blank">Skeuomorph</a>
          <a href="https://en.wikipedia.org/wiki/Apple_Human_Interface_Guidelines#Skeuomorphism" target="_blank">iOS Skeuomorphism</a>
        </p>
      </article>

      <article class="style-card" data-index="7" id="swiss">
        <h3 class="style-title">Swiss Style</h3>
        <p class="style-era">2000s–present</p>
        <p class="style-description">Grid-based layouts, strong typography, and generous whitespace.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Swiss_Style" target="_blank">International Typographic Style</a>
          <a href="https://en.wikipedia.org/wiki/Helvetica" target="_blank">Helvetica</a>
        </p>
      </article>

      <article class="style-card" data-index="8" id="minimalist">
        <h3 class="style-title">Minimalist</h3>
        <p class="style-era">2000s–present</p>
        <p class="style-description">Stripping interfaces to essentials—white space, high-contrast typography, and minimal UI elements.</p>
        <p class="style-links">
          <a href="https://www.nngroup.com/articles/minimalist-web-design/" target="_blank">NNG Guide</a>
          <a href="https://en.wikipedia.org/wiki/Minimalism" target="_blank">Minimalism</a>
        </p>
      </article>

      <article class="style-card" data-index="9" id="responsive">
        <h3 class="style-title">Responsive</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Fluid grids, flexible images, and CSS media queries ensure layouts adapt.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Responsive_web_design" target="_blank">Responsive</a>
          <a href="https://developer.mozilla.org/docs/Web/CSS/Media_Queries" target="_blank">MDN Media Queries</a>
        </p>
      </article>

      <article class="style-card" data-index="10" id="mobile-first">
        <h3 class="style-title">Mobile-First</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Designing for small screens first, then enhancing for larger viewports.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Mobile-first_design" target="_blank">Mobile-First</a>
          <a href="https://www.smashingmagazine.com/2011/01/guidelines-for-responsive-web-design/" target="_blank">Smashing Mag.</a>
        </p>
      </article>

      <article class="style-card" data-index="11" id="flat-design">
        <h3 class="style-title">Flat Design</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Minimalist UI with simple elements, flat colors, no textures.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Flat_design" target="_blank">Flat Design</a>
          <a href="https://en.wikipedia.org/wiki/Metro_(design_language)" target="_blank">Metro UI</a>
        </p>
      </article>

      <article class="style-card" data-index="12" id="semi-flat">
        <h3 class="style-title">Semi-Flat</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Flat foundations augmented with subtle shadows or minimal gradients.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Flat_design#Flat_2.0" target="_blank">Flat 2.0</a>
          <a href="https://uxplanet.org/semi-flat-design-principles-and-examples-6d1d90d82469" target="_blank">UX Planet</a>
        </p>
      </article>

      <article class="style-card" data-index="13" id="parallax">
        <h3 class="style-title">Parallax</h3>
        <p class="style-era">2012–2016</p>
        <p class="style-description">Layered backgrounds move at different speeds for depth.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Parallax_scrolling" target="_blank">Parallax</a>
          <a href="https://developer.mozilla.org/docs/Web/CSS/transform-function/translate3d" target="_blank">MDN</a>
        </p>
      </article>

      <article class="style-card" data-index="14" id="spa">
        <h3 class="style-title">Single-Page App</h3>
        <p class="style-era">2012–2016</p>
        <p class="style-description">JavaScript-driven sites loading content dynamically on one page.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Single-page_application" target="_blank">SPA</a>
          <a href="https://angular.io/guide/architecture" target="_blank">Angular</a>
        </p>
      </article>

      <article class="style-card" data-index="15" id="pwa">
        <h3 class="style-title">Progressive Web App</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">App-like experiences with service workers, offline support, push notifications.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Progressive_web_app" target="_blank">PWA</a>
          <a href="https://web.dev/what-are-pwas/" target="_blank">web.dev</a>
        </p>
      </article>

      <article class="style-card" data-index="16" id="cinematic">
        <h3 class="style-title">Cinematic</h3>
        <p class="style-era">2010s</p>
        <p class="style-description">Full-screen video headers, ambient soundscapes, story layouts.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Video_background" target="_blank">Video Background</a>
          <a href="https://webflow.com/blog/video-backgrounds" target="_blank">Webflow</a>
        </p>
      </article>

      <article class="style-card" data-index="17" id="brutalist">
        <h3 class="style-title">Brutalist</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">Stark pages emphasizing raw HTML, plain fonts, bare-bones layout.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Brutalist_Web_Design" target="_blank">Brutalist Web</a>
          <a href="https://verpex.com/blog/brutalist-web-design/" target="_blank">Verpex</a>
        </p>
      </article>

      <article class="style-card" data-index="18" id="neo-brutalism">
        <h3 class="style-title">Neo-Brutalism</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">Brutalist structure combined with neon or saturated color accents.</p>
        <p class="style-links">
          <a href="https://web.dev/neo-brutalism/" target="_blank">web.dev</a>
          <a href="https://fireart.studio/blog/neo-brutalism/" target="_blank">Fireart</a>
        </p>
      </article>

      <article class="style-card" data-index="19" id="dark-mode">
        <h3 class="style-title">Dark Mode</h3>
        <p class="style-era">2020–present</p>
        <p class="style-description">Light-on-dark color schemes reduce eye strain and feel modern.</p>
        <p class="style-links">
          <a href="https://bluecompass.com/articles/dark-mode-web-design/" target="_blank">Bluecompass</a>
          <a href="https://css-tricks.com/dark-modes-with-css/" target="_blank">CSS-Tricks</a>
        </p>
      </article>

      <article class="style-card" data-index="20" id="neumorphism">
        <h3 class="style-title">Neumorphism</h3>
        <p class="style-era">2020</p>
        <p class="style-description">Soft inner/outer shadows create embossed UI elements.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Neumorphism" target="_blank">Wikipedia</a>
          <a href="https://uxdesign.cc/neumorphism-in-user-interfaces-b47cef3bf3a6" target="_blank">UX Design</a>
        </p>
      </article>

      <article class="style-card" data-index="21" id="glassmorphism">
        <h3 class="style-title">Glassmorphism</h3>
        <p class="style-era">2020</p>
        <p class="style-description">Translucent panels with backdrop blur convey depth.</p>
        <p class="style-links">
          <a href="https://www.nngroup.com/articles/glassmorphism/" target="_blank">NNG</a>
          <a href="https://css-tricks.com/glassmorphism-using-backdrop-filter/" target="_blank">CSS-Tricks</a>
        </p>
      </article>

      <article class="style-card" data-index="22" id="corporate-memphis">
        <h3 class="style-title">Corporate Memphis</h3>
        <p class="style-era">2018–present</p>
        <p class="style-description">Flat geometric illustrations with bendy characters and bright color.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Corporate_Memphis" target="_blank">Wikipedia</a>
          <a href="https://medium.com/corporate-memphis" target="_blank">Medium</a>
        </p>
      </article>
      <article class="style-card" data-index="23" id="retro-web">
        <h3 class="style-title">Retro Web</h3>
        <p class="style-era">2020s</p>
        <p class="style-description">Nostalgic callbacks to ’90s/00s—pixel art, marquee text, neon palettes.</p>
        <p class="style-links">
          <a href="https://fireart.studio/blog/retro-web-design/" target="_blank">Fireart</a>
          <a href="https://99designs.com/blog/web-design/trends/nostalgic-web-design-trends/" target="_blank">99designs</a>
        </p>
      </article>

      <!-- Retro Web Elements (hidden by default, shown when retro theme is active) -->
      <div id="retro-elements" style="display: none;">
        <!-- Animated starfield background -->
        <div class="retro-starfield"></div>

        <!-- Matrix rain effect -->
        <div class="matrix-rain" id="matrix-rain"></div>

        <!-- Top banner with scrolling text -->
        <div class="retro-marquee">
          <marquee behavior="scroll" direction="left" scrollamount="3">
            ★ Welcome to the RETRO WEB ZONE! ★ Best viewed in Netscape Navigator 4.0+ ★ Last updated: Y2K ★ Optimized for 800x600 ★ WebTV Compatible ★
          </marquee>
        </div>

        <!-- Floating badges and awards -->
        <div class="retro-badges">
          <div class="retro-badge badge-1">
            <div class="badge-img netscape-badge"></div>
            <div class="badge-text">BEST VIEWED<br>IN NETSCAPE</div>
          </div>
          <div class="retro-badge badge-2">
            <div class="badge-text">GEOCITIES<br>AWARD WINNER</div>
          </div>
          <div class="retro-badge badge-3">
            <div class="badge-text">MADE WITH<br>NOTEPAD</div>
          </div>
        </div>

        <!-- Blinking elements -->
        <div class="retro-blink-container">
          <span class="retro-blink">NEW!</span>
          <span class="retro-blink">HOT!</span>
          <span class="retro-blink">COOL!</span>
          <span class="retro-blink">UNDER CONSTRUCTION!</span>
        </div>

        <!-- Enhanced webring -->
        <div class="retro-webring">
          <div class="webring-title">[ RETRO WEBRING ]</div>
          <div class="webring-subtitle">Member #42 of 1337</div>
          <div class="webring-nav">
            <a href="#" class="webring-link">&lt;&lt; PREV</a>
            <a href="#" class="webring-link">RANDOM</a>
            <a href="#" class="webring-link">NEXT &gt;&gt;</a>
            <a href="#" class="webring-link">LIST</a>
          </div>
        </div>

        <!-- Enhanced guestbook -->
        <div class="retro-guestbook">
          <div class="guestbook-title">★ SIGN MY GUESTBOOK! ★</div>
          <div class="guestbook-entries">
            <div class="guestbook-entry">
              <strong>xXx_CyberKid_xXx</strong> from <em>Cyberspace</em><br>
              <small>Posted: 12/31/1999 11:59 PM</small><br>
              "OMG this site is SO COOL!!! Your HTML skillz are 1337! 🌟✨"
            </div>
            <div class="guestbook-entry">
              <strong>WebMaster2000</strong> from <em>The Matrix</em><br>
              <small>Posted: 01/01/2000 12:00 AM</small><br>
              "Y2K COMPLIANT! Nice use of &lt;blink&gt; tags! 💻🔥"
            </div>
          </div>
        </div>

        <!-- Enhanced hit counter -->
        <div class="retro-hit-counter">
          <div class="counter-title">VISITOR COUNTER</div>
          <div class="counter-display">
            <span class="counter-digit">1</span>
            <span class="counter-digit">2</span>
            <span class="counter-digit">3</span>
            <span class="counter-digit">4</span>
            <span class="counter-digit">5</span>
            <span class="counter-digit">6</span>
          </div>
          <div class="counter-text">You are visitor #123456</div>
          <div class="counter-text">Since Y2K</div>
        </div>

        <!-- Retro navigation frame simulation -->
        <div class="retro-nav-frame">
          <div class="nav-title">NAVIGATION</div>
          <div class="nav-links">
            <a href="#" class="nav-link">🏠 HOME</a>
            <a href="#" class="nav-link">📧 EMAIL ME</a>
            <a href="#" class="nav-link">🔗 LINKS</a>
            <a href="#" class="nav-link">📷 PHOTOS</a>
            <a href="#" class="nav-link">💾 DOWNLOADS</a>
          </div>
        </div>

        <!-- Retro status bar -->
        <div class="retro-status-bar">
          <span id="status-text">Welcome to my homepage! Loading awesome content...</span>
        </div>
      </div>

      <article class="style-card" data-index="24" id="y2k-web">
        <h3 class="style-title">Y2K Web</h3>
        <p class="style-era">2020s</p>
        <p class="style-description">Chrome buttons, metallic gradients, holographic effects, and futuristic typography that captures the millennium's digital optimism.</p>
        <p class="style-links">
          <a href="https://fireart.studio/blog/y2k-web-design/" target="_blank">Fireart Studio</a>
          <a href="https://medium.com/y2k-web" target="_blank">Medium Y2K</a>
          <a href="https://www.awwwards.com/y2k-aesthetic-web-design.html" target="_blank">Awwwards</a>
        </p>
      </article>

      <!-- Y2K Web Elements (hidden by default, shown when Y2K theme is active) -->
      <div id="y2k-elements" style="display: none;">
        <!-- Floating chrome orbs -->
        <div class="y2k-orbs">
          <div class="chrome-orb orb-1"></div>
          <div class="chrome-orb orb-2"></div>
          <div class="chrome-orb orb-3"></div>
          <div class="chrome-orb orb-4"></div>
        </div>

        <!-- Holographic text overlay -->
        <div class="holographic-overlay">
          <div class="holo-text">MILLENNIUM</div>
          <div class="holo-text">DIGITAL</div>
          <div class="holo-text">FUTURE</div>
        </div>

        <!-- Y2K cursor trail -->
        <div class="cursor-trail" id="cursor-trail"></div>

        <!-- Floating UI panels -->
        <div class="floating-panels">
          <div class="ui-panel panel-1">
            <div class="panel-header">SYSTEM STATUS</div>
            <div class="panel-content">
              <div class="status-bar">
                <span>CPU:</span>
                <div class="progress-bar"><div class="progress" style="width: 78%"></div></div>
              </div>
              <div class="status-bar">
                <span>RAM:</span>
                <div class="progress-bar"><div class="progress" style="width: 45%"></div></div>
              </div>
              <div class="status-bar">
                <span>NET:</span>
                <div class="progress-bar"><div class="progress" style="width: 92%"></div></div>
              </div>
            </div>
          </div>

          <div class="ui-panel panel-2">
            <div class="panel-header">MILLENNIUM COUNTDOWN</div>
            <div class="panel-content">
              <div class="countdown-display">
                <div class="countdown-unit">
                  <span class="countdown-number">00</span>
                  <span class="countdown-label">DAYS</span>
                </div>
                <div class="countdown-unit">
                  <span class="countdown-number">00</span>
                  <span class="countdown-label">HRS</span>
                </div>
                <div class="countdown-unit">
                  <span class="countdown-number">00</span>
                  <span class="countdown-label">MIN</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Particle system container -->
        <div class="particle-system" id="particle-system"></div>
      </div>

      <article class="style-card" data-index="25" id="webgl-3d">
        <h3 class="style-title">WebGL & 3D</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">Interactive 3D graphics, model viewers, VR/AR in browser.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/WebGL" target="_blank">WebGL</a>
          <a href="https://threejs.org/" target="_blank">three.js</a>
        </p>
      </article>

      <!-- WebGL & 3D Elements (hidden by default, shown when WebGL theme is active) -->
      <div id="webgl-3d-elements" style="display: none;">
        <!-- 3D Scene Container -->
        <div class="webgl-scene-container">
          <canvas id="webgl-canvas" class="webgl-canvas"></canvas>
          <div class="scene-overlay">
            <div class="hud-element hud-top-left">
              <div class="hud-title">3D RENDERER</div>
              <div class="hud-stats">
                <div class="stat-line">FPS: <span id="fps-counter">60</span></div>
                <div class="stat-line">VERTICES: <span id="vertex-counter">1,024</span></div>
                <div class="stat-line">TRIANGLES: <span id="triangle-counter">512</span></div>
              </div>
            </div>
            <div class="hud-element hud-top-right">
              <div class="hud-title">CONTROLS</div>
              <div class="control-hint">MOUSE: Rotate</div>
              <div class="control-hint">WHEEL: Zoom</div>
              <div class="control-hint">CLICK: Interact</div>
            </div>
          </div>
        </div>

        <!-- Floating 3D Panels -->
        <div class="floating-3d-panels">
          <div class="panel-3d panel-1" data-depth="100">
            <div class="panel-header">GEOMETRY</div>
            <div class="panel-content">
              <div class="geometry-controls">
                <button class="geo-btn active" data-shape="cube">CUBE</button>
                <button class="geo-btn" data-shape="sphere">SPHERE</button>
                <button class="geo-btn" data-shape="torus">TORUS</button>
                <button class="geo-btn" data-shape="dodecahedron">DODECA</button>
              </div>
            </div>
          </div>

          <div class="panel-3d panel-2" data-depth="150">
            <div class="panel-header">MATERIALS</div>
            <div class="panel-content">
              <div class="material-controls">
                <button class="mat-btn active" data-material="metallic">METAL</button>
                <button class="mat-btn" data-material="glass">GLASS</button>
                <button class="mat-btn" data-material="neon">NEON</button>
                <button class="mat-btn" data-material="hologram">HOLO</button>
              </div>
            </div>
          </div>

          <div class="panel-3d panel-3" data-depth="200">
            <div class="panel-header">LIGHTING</div>
            <div class="panel-content">
              <div class="lighting-controls">
                <div class="light-slider">
                  <label>AMBIENT</label>
                  <input type="range" min="0" max="100" value="30" class="slider" id="ambient-light">
                </div>
                <div class="light-slider">
                  <label>DIRECTIONAL</label>
                  <input type="range" min="0" max="100" value="70" class="slider" id="directional-light">
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Particle System Background -->
        <div class="particle-field" id="particle-field-3d"></div>

        <!-- 3D Navigation Cube -->
        <div class="nav-cube-container">
          <div class="nav-cube">
            <div class="cube-face front">F</div>
            <div class="cube-face back">B</div>
            <div class="cube-face right">R</div>
            <div class="cube-face left">L</div>
            <div class="cube-face top">T</div>
            <div class="cube-face bottom">Bot</div>
          </div>
        </div>

        <!-- Holographic UI Elements -->
        <div class="holographic-ui">
          <div class="holo-ring ring-1"></div>
          <div class="holo-ring ring-2"></div>
          <div class="holo-ring ring-3"></div>
          <div class="holo-grid"></div>
        </div>

        <!-- 3D Model Showcase -->
        <div class="model-showcase">
          <div class="model-viewer" id="model-viewer-1">
            <div class="model-placeholder">
              <div class="wireframe-cube"></div>
            </div>
            <div class="model-info">
              <div class="model-name">GEOMETRIC PRIMITIVE</div>
              <div class="model-details">Vertices: 8 | Faces: 6</div>
            </div>
          </div>
        </div>
      </div>

      <article class="style-card" data-index="26" id="inclusive-design">
        <h3 class="style-title">Inclusive Design</h3>
        <p class="style-era">Ongoing</p>
        <p class="style-description">ARIA roles, semantic HTML, user-centric accessibility.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Accessible_design" target="_blank">Accessible Design</a>
          <a href="https://www.w3.org/WAI/fundamentals/accessibility-intro/" target="_blank">W3C</a>
        </p>
      </article>
    </main>
    <section id="outro" class="intro outro">
      <h3><strong>Under The Hood</strong></h3>
      <p>Under the hood, the page relies on a combination of HTML, CSS, and JavaScript (with jQuery) to switch between design themes dynamically. A &lt;style id="theme-style"&gt; element is injected into the document head to override default styles with the current theme’s custom CSS. The page initializes a themes array in the video.js script, where each object includes a theme name and its CSS rules; a separate dark-mode variant is generated for each theme. When you trigger a theme change—either by clicking on a card, Gantt Timeline element, or using the keyboard shortcut—the script updates the injected CSS, reformats it for readability in the editor, and logs events (including Google Analytics tracking and console messages) for debugging. Special handling is included for cinematic themes, which preload and alternate background videos to recreate the immersive, full-screen intro experiences of earlier Flash-driven sites</p>
    </section>

    <section class="html-editor">
      <h2>Current HTML</h2>
      <textarea id="html-editor" editable></textarea>
    </section>

    <!-- jQuery & Migrate -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-migrate-3.4.1.min.js"></script>
    <script src="https://unpkg.com/prettier@2.8.8/standalone.js"></script>
    <script src="https://unpkg.com/prettier@2.8.8/parser-postcss.js"></script>
    <script>
      // Visitor counter code
      var counterContainer = document.querySelector('#vistor-counter')
      var resetButton = document.querySelector('#reset')
      var visitCount = localStorage.getItem('page_view')
      
      // Check if page_view entry is present
      if (visitCount) {
        visitCount = Number(visitCount) + 1
        localStorage.setItem('page_view', visitCount)
      } else {
        visitCount = 1
        localStorage.setItem('page_view', 1)
      }
      counterContainer.innerHTML = visitCount


      // Create Light and Dark Arrays 
      const lightThemes = [{
        'id': 'default',
        'handle': 'defaultStyle',
        'name': 'Default',
        'lightCss': `
            body { 
              /* fill the full viewport */
              min-height: 100vh;
              margin: 0;
              /* repeating diagonal stripes */
              background-image: repeating-linear-gradient(
                  var(--stripe-angle),
                  var(--stripe-color) 0,
                  var(--stripe-color) var(--stripe-width),
                transparent var(--stripe-width),
                  transparent var(--stripe-spacing)
              );
              font-family: 'Comic Sans MS', 'Comic Sans', cursive;
            }
            /* Ensure main content scrolls above video */
            main {
              position: relative;
              max-width: 85%;
              align-items: center;
              justify-content: center;
              padding: 0;
              /* Remove any padding */
              margin: auto;
              /* Remove any margins */
              top: 5vh;
              max-width: 1200px;
              /* Adjust this value as needed */
              display: flex;
              flex-wrap: wrap !important;
              gap: 20px;
            }
            .content-wrapper {
                display: block;
                position: relative;
                left: 0;
                top: 0;
              }
              .header-content h1 { 
                color: #ff0000; 
                font-size: 3vw;
                margin-bottom: 5px;
              }
              .header-content p { 
                color: #ffa600; 
                font-size: 1.5vw;
              }
            .era-timeline { 
              display: block !important;
              background: var(--bg-color);
              opacity: var(--opacity);
            }
            .style-card {
              background: #ffffcc;
              border: 3px outset #cc9966;
            }
            .style-description {
              color: #333333;
            }
            .style-title {
              color: #990000;
              font-weight: bold;
            }
            .style-links a {
              color: #0000ff;
              text-decoration: underline;
            }
            .extra-under { display: block !important; }
            .mermaid {
              background:rgb(245, 245, 243);
              color:rgb(42, 44, 20);
            }
            /* CSS editor styles */
            #css-editor,
            #html-editor {
              font-family: 'Courier New', monospace;
              line-height: 1.5;
              padding: 10px;
              border-radius: 4px;
              margin: 10px 0;
              border: 1px solid #ccc;
              background-color: #f9f9f9;
              resize: vertical;
            }
            
            .css-editor {
              margin: auto;
              max-width: 800vw;
              min-width: 500px;
              min-height: 150px;
              text-align: center;
              display: block;
            }
            
            .css-editor {
              margin: auto;
              max-width: 800vw;
              min-width: 500px;
              min-height: 150px;
              text-align: center;
              display: block;
            }
            
            .html-editor {
              margin: auto;
              max-width: calc(100% - 20px);
              min-width: 500px;
              min-height: 500px;
              text-align: center;
              display: block;
            }
            .style-card {
              cursor: pointer;
              transition: transform 0.2s ease, box-shadow 0.2s ease;
            }
            .style-card:hover {
              transform: translateY(-3px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
            /* Tooltip to indicate clickable elements */
            [data-tooltip]:before {
              content: attr(data-tooltip);
              position: absolute;
              opacity: 0;
              transition: all 0.15s ease;
              padding: 5px 10px;
              color: #fff;
              border-radius: 4px;
              background: rgba(0, 0, 0, 0.8);
              pointer-events: none;
              z-index: 10;
              white-space: nowrap;
              transform: translateY(10px);
            }
            [data-tooltip]:hover:before {
              opacity: 1;
              transform: translateY(0);
            }
          `,
        'darkCss': `
          body { 
            /* fill the full viewport */
            min-height: 100vh;
            margin: 0;
  
            /* repeating diagonal stripes */
            background-image: repeating-linear-gradient(
                calc(3.125 * var(--stripe-angle)),
                var(--dark-stripe-color) 0,
                var(--dark-stripe-color) var(--stripe-width),
                rgba(0, 0, 0, 0.8) calc(1.125 * var(--stripe-width)),
                rgba(0, 0, 0, 0.8) var(--stripe-spacing)
            );
            font-family: 'Comic Sans MS', 'Comic Sans', cursive;
          }
              main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .content-wrapper {
            display: block;
            position: relative;
            left: 0;
            top: 0;
          }
          .header-content h1 { 
            color: #ff0000; 
            font-size: 6vw;
            margin-bottom: 5px;
          }
          .header-content p { 
            color: #ffa600; 
            font-size: 4vw;
          }
          .era-timeline { 
            display: block !important;
            background: var(--inverse-bg-color, #222);
            opacity: var(--opacity, 0.8);
          }
          .style-links a {
            color: #66ccff;
            text-decoration: underline;
          }
          .extra-under { display: block !important; }
          .mermaid {
            background: #333300;
            color: #f0f0f0;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
          }, {
            'id': 'vernacular',
            'handle': 'vernacularStyle',
            'name': 'Vernacular / Geocities',
            'lightCss': `
              body { 
                /* fill the full viewport */
                min-height: 100vh;
                margin: 0;
                /* repeating diagonal stripes */
                background-image: repeating-linear-gradient(
                    var(--stripe-angle),
                    var(--stripe-color) 0,
                    var(--stripe-color) var(--stripe-width),
                  transparent var(--stripe-width),
                    transparent var(--stripe-spacing)
                );
                font-family: 'Comic Sans MS', 'Comic Sans', cursive;
              }
              .content-wrapper {
                  display: block;
                  position: relative;
                  left: 0;
                  top: 0;
                }
            .header-content h1 { 
              color: #ff0000; 
              font-size: 3vw;
              margin-bottom: 5px;
            }
            .header-content p { 
              color: #ffa600; 
              font-size: 1.5vw;
            }
          .era-timeline { 
            display: block !important;
            background: var(--bg-color);
            opacity: var(--opacity);
          }
          .style-card {
            background: #ffffcc;
          }
          .style-description {
            color: #333333;
          }
          .style-title {
            color: #990000;
            font-weight: bold;
          }
          .style-links a {
            color: #0000ff;
            text-decoration: underline;
          }
          .extra-under { display: block !important; }
          .mermaid {
            background:rgb(245, 245, 243);
            color:rgb(42, 44, 20);
          }
          .counter {
            display: flex;
            flex: row wrap;
            padding: 2rem;
          }
          #under-construction {
            position: absolute;
            margin: 2rem;
            width: 100%;
            height: 100%;
            left: 40%;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
          'darkCss': `
          body { 
            /* fill the full viewport */
            min-height: 100vh;
            margin: 0;
  
            /* repeating diagonal stripes */
            background-image: repeating-linear-gradient(
                calc(3.125 * var(--stripe-angle)),
                var(--dark-stripe-color) 0,
                var(--dark-stripe-color) var(--stripe-width),
                rgba(0, 0, 0, 0.8) calc(1.125 * var(--stripe-width)),
                rgba(0, 0, 0, 0.8) var(--stripe-spacing)
            );
            font-family: 'Comic Sans MS', 'Comic Sans', cursive;
          }
          .content-wrapper {
              display: block;
              position: relative;
              left: 0;
              top: 0;
            }
            .header-content h1 { 
              color: #ff0000; 
              font-size: 3vw;
              margin-bottom: 5px;
            }
            .header-content p { 
              color: #ffa600; 
              font-size: 1.5vw;
            }
          .era-timeline { 
            display: block !important;
            background: var(--bg-color);
            opacity: var(--opacity);
          }
          .style-card {
            background: #ffffcc;
          }
          .style-description {
            color: #333333;
          }
          .style-title {
            color: #990000;
            font-weight: bold;
          }
          .style-links a {
            color: #0000ff;
            text-decoration: underline;
          }
          .extra-under { display: block !important; }
          .mermaid {
            background:rgb(245, 245, 243);
            color:rgb(42, 44, 20);
          }
          .counter {
            display: flex;
            flex: row wrap;
            padding: 2rem;
            color: #dddddd;
          }
          #under-construction {
            position: absolute;
            margin: 2rem;
            width: 100%;
            height: 100%;
            left: 40%;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'table-frames',
          'handle': 'table-framesStyle',
          'name': 'Table-Based Design',
          'lightCss': `
          body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            color: #333;
          }
          main {
            display: table;
            width: 100%;
            border-collapse: collapse;
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
          }
          .style-card {
            display: table-cell;
            border: 1px solid #999;
            padding: 10px;
            background: white;
          }
          #main-table { display: block !important; }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: Arial, sans-serif;
          background: #f0f0f0;
          color: #333;
        }
        main {
          display: table;
          width: 100%;
          border-collapse: collapse;
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
        }
        .style-card {
          display: table-cell;
          border: 1px solid #999;
          padding: 10px;
          background: white;
        }
        #main-table { display: block !important; }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'framesets',
        'handle': 'framesetsStyle',
        'name': 'Framesets',
        'lightCss': `
          body {
            margin: 0;
            padding: 0;
            font-family: Verdana, sans-serif;
          }
          .frame-nav, .frame-content {
            display: block !important;
            position: fixed;
            border: 2px inset #ccc;
            background: #f0f0f0;
          }
          .frame-nav {
            left: 0;
            top: 0;
            width: 200px;
            height: 100%;
            overflow: auto;
            padding: 10px;
          }
          .frame-content {
            left: 200px;
            top: 0;
            width: calc(100% - 200px);
            height: 100%;
            overflow: auto;
            padding: 10px;
            background: white;
          }
          main {
            margin-left: 200px;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          margin: 0;
          padding: 0;
          font-family: Verdana, sans-serif;
        }
        .frame-nav, .frame-content {
          display: block !important;
          position: fixed;
          border: 2px inset #ccc;
          background: #f0f0f0;
        }
        .frame-nav {
          left: 0;
          top: 0;
          width: 200px;
          height: 100%;
          overflow: auto;
          padding: 10px;
        }
        .frame-content {
          left: 200px;
          top: 0;
          width: calc(100% - 200px);
          height: 100%;
          overflow: auto;
          padding: 10px;
          background: white;
        }
        main {
          margin-left: 200px;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'flash',
        'handle': 'flashStyle',
        'name': 'Flash-Based Design',
        'lightCss': `
          body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            text-align: center;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .extra-flash-intro { 
            display: block !important; 
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1000;
          }
          .style-card {
            background: #333;
            border-radius: 0;
            border: 1px solid #666;
            color: #fff;
          }
          .style-title {
            color: #ff9900;
          }
          .style-links a {
            color: #66ccff;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          background: #000;
          color: #fff;
          font-family: Arial, sans-serif;
          text-align: center;
        }
        /* Ensure main content scrolls above video */
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .extra-flash-intro { 
          display: block !important; 
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: #000;
          z-index: 1000;
        }
        .style-card {
          background: #333;
          border-radius: 0;
          border: 1px solid #666;
          color: #fff;
        }
        .style-title {
          color: #ff9900;
        }
        .style-links a {
          color: #66ccff;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'web2',
        'handle': 'web2Style',
        'name': 'Web 2.0 Glossy',
        'lightCss': `
          body {
            font-family: 'Lucida Grande', Arial, sans-serif;
            background: #f6f6f6;
          }
            /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            border-radius: 8px;
            border: 1px solid #ddd;
            background: linear-gradient(to bottom, #ffffff 0%, #f6f6f6 100%);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          .style-title {
            color: #0066cc;
            text-shadow: 0 1px 0 white;
          }
          .beta-badge {
            display: inline-block !important;
            background: #ff6600;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            margin-left: 5px;
            text-transform: uppercase;
            font-weight: bold;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Lucida Grande', Arial, sans-serif;
          background: #f6f6f6;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          border-radius: 8px;
          border: 1px solid #ddd;
          background: linear-gradient(to bottom, #ffffff 0%, #f6f6f6 100%);
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .style-title {
          color: #0066cc;
          text-shadow: 0 1px 0 white;
        }
        .beta-badge {
          display: inline-block !important;
          background: #ff6600;
          color: white;
          font-size: 10px;
          padding: 2px 5px;
          border-radius: 10px;
          margin-left: 5px;
          text-transform: uppercase;
          font-weight: bold;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'skeuomorphic',
        'handle': 'skeuomorphicStyle',
        'name': 'Skeuomorphic Design',
        'lightCss': `
          body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            position: relative;
            margin: 40px auto;
            width: 400px;
            height: 350px;
            background: #fff;
            border-radius: 1px;
          }
          /* Post-it note colors for each card */
          .style-card:nth-child(5n+1) {
            background: #FFFA99; /* Classic yellow */
          }
          .style-card:nth-child(5n+2) {
            background: #FF9E9E; /* Salmon pink */
          }
          .style-card:nth-child(5n+3) {
            background: #9EEBFF; /* Sky blue */
          }
          .style-card:nth-child(5n+4) {
            background: #B1FF9E; /* Mint green */
          }
          .style-card:nth-child(5n+5) {
            background: #E19EFF; /* Lavender */
          }
          .style-card::before,
          .style-card::after {
            content: '';
            position: absolute;
            bottom: 10px;
            width: 40%;
            height: 10px;
            box-shadow: 0 5px 14px rgba(0,0,0,.7);
            z-index: -1;
            transition: all .3s ease-in-out;
          }

          .style-card::before {
            left: 15px;
            transform: skew(-5deg) rotate(-5deg);
          }

          .style-card::after {
            right: 15px;
            transform: skew(5deg) rotate(5deg);
          }

          .style-card:hover::before,
          .style-card:hover::after {
            box-shadow: 0 2px 14px rgba(0,0,0,.4);
          }

          .style-card:hover::before {
            left: 5px;
          }

          .style-card:hover::after {
            right: 5px;
          }
          .style-title {
            font-size: 1.6rem;
            cursor: pointer;
            color: rgba(0, 122, 204, 0.9);
            display: inline-block;
            margin-bottom: 0.25rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 400;
            font-style: normal;
            padding: 0 0 0 2rem;
          }
          .style-era {
            font-style: italic;
            margin-bottom: 0.5rem;
            padding: 0 0 0 2rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 300;
            font-style: normal;
            opacity: 0.7;
          }
          
          .style-description {
            margin-bottom: 0.5rem;
            padding: 0 1rem 0 2rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 300;
            font-style: normal;
            opacity: 0.7;
          }
          
          .style-links a {
            margin-right: 0.5rem;
            color: #007acc;
            padding: 0 0 0 2rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 100;
            font-style: normal;
            opacity: 0.7;
          }
          .era-timeline {
            display: block !important;
            background: url('https://img.freepik.com/free-photo/white-gypsum-wall_1194-6990.jpg?t=st=1746443052~exp=1746446652~hmac=a2420a81ff31a63454846c9c05c450b65b5a3982ffc27fd89a63d2b85f7ca563&w=1380');
            pointer-events: none;
            
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
        `,
        'darkCss': `
          body {
            background: #222;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: url('https://i.imgur.com/Ncrkb8L.png') repeat;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.5);
            border: 1px solid #444;
            color: #e0e0e0;
          }
          .card-texture {
            display: block !important;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://i.imgur.com/FL3SmIu.jpeg');
            opacity: 0.05;
            pointer-events: none;
          }
          .style-title {
            color: #bb86fc;
          }
          #css-editor {
            background-color: #333;
            color: #e0e0e0;
            border-color: #444;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.5);
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'swiss',
          'handle': 'swissStyle',
          'name': 'Swiss Style Design',
          'lightCss': `
          body {
            font-family: Helvetica, Arial, sans-serif;
            background: white;
            color: black;
            line-height: 1.5;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          main {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 20px;
          }
          .style-card {
            grid-column: span 4;
            background: white;
            border: none;
            border-bottom: 2px solid black;
            border-radius: 0;
            padding: 20px 0;
          }
          .style-title {
            font-size: 18px;
            font-weight: bold;
            color: black;
          }
          .grid-overlay {
            display: block !important;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
              90deg,
              rgba(255,0,0,0.03),
              rgba(255,0,0,0.03) 8.33%,
              transparent 8.33%,
              transparent 16.66%
            );
            pointer-events: none;
            z-index: 1000;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
          'darkCss': `
          body {
            font-family: Helvetica, Arial, sans-serif;
            background: #121212;
            color: white;
            line-height: 1.5;
          }
          main {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 20px;
        
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */

            gap: 20px;
          }
          .style-card {
            grid-column: span 4;
            background: #1a1a1a;
            border: none;
            border-bottom: 2px solid white;
            border-radius: 0;
            padding: 20px 0;
            color: white;
          }
          .style-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
          }
          .style-links a {
            color: #bb86fc;
          }
          #css-editor {
            background-color: #1a1a1a;
            color: white;
            border-color: white;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'minimalist',
          'handle': 'minimalistStyle',
          'name': 'Minimalist',
          'lightCss': `
          body {
            font-family: 'Inter', sans-serif;
            background: white;
            color: #333;
            line-height: 1.6;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: white;
            border: none;
            border-radius: 0;
            padding: 30px 0;
            box-shadow: none;
          }
          .style-title {
            font-weight: 300;
            font-size: 24px;
            color: #000;
          }
          .style-description {
            font-weight: 300;
          }
          .style-links a {
            color: #000;
            text-decoration: none;
            border-bottom: 1px solid #000;
            padding-bottom: 2px;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
            font-family: 'Inter', sans-serif;
            background: #121212;
            color: #e0e0e0;
            line-height: 1.6;
          }
            main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: #1a1a1a;
            border: none;
            border-radius: 0;
            padding: 30px 0;
            box-shadow: none;
            color: #e0e0e0;
          }
          .style-title {
            font-weight: 300;
            font-size: 24px;
            color: #fff;
          }
          .style-description {
            font-weight: 300;
          }
          .style-links a {
            color: #bb86fc;
            text-decoration: none;
            border-bottom: 1px solid #bb86fc;
            padding-bottom: 2px;
          }
          #css-editor {
            background-color: #1a1a1a;
            color: #e0e0e0;
            border: none;
            border-bottom: 1px solid #333;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'responsive',
          'handle': 'responsiveStyle',
          'name': 'Responsive Design',
          'lightCss': `
          body {
            font-family: 'Open Sans', sans-serif;
            background: #f9f9f9;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
          .style-card {
            flex: 0 0 calc(33.333% - 20px);
            margin-bottom: 30px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
          }
          @media (max-width: 992px) {
            .style-card {
              flex: 0 0 calc(50% - 15px);
            }
          }
          @media (max-width: 576px) {
            .style-card {
              flex: 0 0 100%;
            }
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `,
      'darkCss': `
        body {
          font-family: 'Open Sans', sans-serif;
          background: #f9f9f9;
        }
        main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
        .style-card {
          flex: 0 0 calc(33.333% - 20px);
          margin-bottom: 30px;
          background: white;
          border-radius: 5px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          transition: all 0.3s ease;
        }
        @media (max-width: 992px) {
          .style-card {
            flex: 0 0 calc(50% - 15px);
          }
        }
        @media (max-width: 576px) {
          .style-card {
            flex: 0 0 100%;
          }
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'mobile-first',
        'handle': 'mobile-firstStyle',
        'name': 'Mobile-First Design',
        'lightCss': `
          body {
            font-family: 'Roboto', sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
          }
          main {
            padding: 10px;
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            width: 100%;
            margin-bottom: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 15px;
          }
          @media (min-width: 768px) {
            main {
              padding: 20px;
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
            }
            .style-card {
              width: calc(50% - 10px);
            }
          }
          @media (min-width: 992px) {
            .style-card {
              width: calc(33.333% - 15px);
            }
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Roboto', sans-serif;
          font-size: 16px;
          line-height: 1.5;
          color: #333;
          background: #f5f5f5;
        }
        main {
          padding: 10px;
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          width: 100%;
          margin-bottom: 15px;
          background: white;
          border-radius: 5px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          padding: 15px;
        }
        @media (min-width: 768px) {
          main {
            padding: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
          .style-card {
            width: calc(50% - 10px);
          }
        }
        @media (min-width: 992px) {
          .style-card {
            width: calc(33.333% - 15px);
          }
        }
        /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'flat',
        'handle': 'flatStyle',
        'name': 'Flat Design',
        'lightCss': `
          body {
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            background: #f9f9f9;
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: none;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
          }
          .style-title {
            color: #0078d7;
          }
          .style-links a {
            color: #0078d7;
            text-decoration: none;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
      body {
          font-family: 'Segoe UI', 'Roboto', sans-serif;
          background: #f9f9f9;
          color: #333;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: white;
          border: none;
          border-radius: 2px;
          box-shadow: 0 1px 2px rgba(0,0,0,0.1);
          transition: all 0.2s ease;
        }
        .style-title {
          color: #0078d7;
        }
        .style-links a {
          color: #0078d7;
          text-decoration: none;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'semi-flat',
        'handle': 'semi-flatStyle',
        'name': 'Semi-Flat Design',
        'lightCss': `
          body {
            font-family: 'SF Pro Text', 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
          }
          .style-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
          }
          .style-title {
            color: #007aff;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'SF Pro Text', 'Roboto', sans-serif;
          background: #f5f5f5;
          color: #333;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: white;
          border: none;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          transition: all 0.3s ease;
        }
        .style-card:hover {
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
          transform: translateY(-2px);
        }
        .style-title {
          color: #007aff;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'parallax',
        'handle': 'parallaxStyle',
        'name': 'Parallax Design',
        'lightCss': `
          body {
            font-family: 'Montserrat', sans-serif;
            background: #222;
            color: white;
            perspective: 1px;
            height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;
          }
          
          /* Parallax background layers */
          .parallax-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
          }
          
          .parallax-layer {
            position: absolute;
            width: 200%;
            height: 100%;
            background-repeat: repeat-x;
            background-size: auto 100%;
            will-change: transform;
          }
          
          .layer-1 {
            background-image: url('https://images.unsplash.com/photo-1506318137071-a8e063b4bec0?w=1200');
            animation: parallaxScroll 60s linear infinite;
            opacity: 0.3;
          }
          
          .layer-2 {
            background-image: url('https://images.unsplash.com/photo-1534447677768-be436bb09401?w=1200');
            animation: parallaxScroll 40s linear infinite;
            opacity: 0.2;
          }
          
          .layer-3 {
            background-image: url('https://images.unsplash.com/photo-1520034475321-cbe63696469a?w=1200');
            animation: parallaxScroll 20s linear infinite;
            opacity: 0.1;
          }
          
          @keyframes parallaxScroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
          
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            transform-style: preserve-3d;
            z-index: 1;
          }
          
          .style-card {
            background: rgba(255,255,255,0.8);
            color: #333;
            border: none;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transform: translateZ(0);
            backdrop-filter: blur(3px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
          
          .style-card:hover {
            transform: translateZ(5px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
          }
          
          /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Montserrat', sans-serif;
          background: #111;
          color: white;
          perspective: 1px;
          height: 100vh;
          overflow-x: hidden;
          overflow-y: auto;
          position: relative;
        }
        
        /* Parallax background layers */
        .parallax-bg {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: -1;
          overflow: hidden;
        }
        
        .parallax-layer {
          position: absolute;
          width: 200%;
          height: 100%;
          background-repeat: repeat-x;
          background-size: auto 100%;
          will-change: transform;
        }
        
        .layer-1 {
          background-image: url('https://images.unsplash.com/photo-1506318137071-a8e063b4bec0?w=1200');
          animation: parallaxScroll 60s linear infinite;
          opacity: 0.3;
        }
        
        .layer-2 {
          background-image: url('https://images.unsplash.com/photo-1534447677768-be436bb09401?w=1200');
          animation: parallaxScroll 40s linear infinite;
          opacity: 0.2;
          filter: hue-rotate(180deg) brightness(0.5);
        }
        
        .layer-3 {
          background-image: url('https://images.unsplash.com/photo-1520034475321-cbe63696469a?w=1200');
          animation: parallaxScroll 20s linear infinite;
          opacity: 0.1;
          filter: hue-rotate(180deg) brightness(0.5);
        }
        
        @keyframes parallaxScroll {
          0% { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
        
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          margin: auto;
          top: 5vh;
          max-width: 1200px;
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
          transform-style: preserve-3d;
          z-index: 1;
        }
        
        .style-card {
          background: rgba(30,30,30,0.8);
          color: #eee;
          border: none;
          border-radius: 5px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.5);
          transform: translateZ(0);
          backdrop-filter: blur(3px);
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .style-card:hover {
          transform: translateZ(5px) scale(1.02);
          box-shadow: 0 8px 25px rgba(0,0,0,0.6);
        }
        
        /* CSS editor styles */
        #css-editor,
        #html-editor {
          font-family: 'Courier New', monospace;
          line-height: 1.5;
          padding: 10px;
          border-radius: 4px;
          margin: 10px 0;
          border: 1px solid #444;
          background-color: #222;
          color: #eee;
          resize: vertical;
        }
        
        .css-editor {
          margin: auto;
          max-width: 800vw;
          min-width: 500px;
          min-height: 150px;
          text-align: center;
          display: block;
        }
        
        .html-editor {
          margin: auto;
          max-width: calc(100% - 20px);
          min-width: 500px;
          min-height: 500px;
          text-align: center;
          display: block;
        }
        /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'spa',
        'handle': 'single-page-appStyle',
        'name': 'Single-Page App Design',
        'lightCss': `
          body {
            font-family: 'Roboto', sans-serif;
            background: #fafafa;
            color: #333;
            padding-top: 60px;
          }
              /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 100;
            padding: 15px 0;
          }
          .style-card {
            background: white;
            border: none;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            transition: all 0.3s cubic-bezier(.25,.8,.25,1);
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Roboto', sans-serif;
          background: #fafafa;
          color: #333;
          padding-top: 60px;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        header {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          background: white;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          z-index: 100;
          padding: 15px 0;
        }
        .style-card {
          background: white;
          border: none;
          border-radius: 2px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.12);
          transition: all 0.3s cubic-bezier(.25,.8,.25,1);
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'pwa',
        'handle': 'pwaStyle',
        'name': 'Progressive Web App Design',
        'lightCss': `
          body {
            font-family: 'Google Sans', 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #202124;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
          }
          main {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 24px;
            z-index: 1;
          }
          header {
            position: sticky;
            top: 0;
            background: #1a73e8;
            color: white;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .style-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            will-change: transform;
          }
          .style-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.16), 0 4px 8px rgba(0,0,0,0.23);
          }
          .style-title {
            color: #1a73e8;
            font-size: 1.25rem;
            font-weight: 500;
          }
          #css-editor,
          #html-editor {
            font-family: 'Roboto Mono', monospace;
            line-height: 1.5;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            border: 1px solid #dadce0;
            background-color: #f8f9fa;
            resize: vertical;
            transition: box-shadow 0.2s;
          }
          #css-editor:focus,
          #html-editor:focus {
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.4);
            outline: none;
          }
          .css-editor, .html-editor {
            margin: auto;
            width: 100%;
            max-width: 800px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Google Sans', 'Roboto', sans-serif;
            background: #202124;
            color: #e8eaed;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
          }
          main {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 24px;
            z-index: 1;
          }
          header {
            position: sticky;
            top: 0;
            background: #1a73e8;
            color: white;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .style-card {
            background: #2d2e30;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.25), 0 1px 2px rgba(0,0,0,0.35);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            will-change: transform;
          }
          .style-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 4px 8px rgba(0,0,0,0.4);
          }
          .style-title {
            color: #8ab4f8;
            font-size: 1.25rem;
            font-weight: 500;
          }
          #css-editor,
          #html-editor {
            font-family: 'Roboto Mono', monospace;
            line-height: 1.5;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            border: 1px solid #5f6368;
            background-color: #2d2e30;
            color: #e8eaed;
            resize: vertical;
            transition: box-shadow 0.2s;
          }
          #css-editor:focus,
          #html-editor:focus {
            box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.4);
            outline: none;
          }
          .css-editor, .html-editor {
            margin: auto;
            width: 100%;
            max-width: 800px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'cinematic',
        'handle': 'cinematicStyle',
        'name': 'Cinematic Design',
        'lightCss': `
          body {
            font-family: 'Playfair Display', serif;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
            margin: 0;
            padding: 0;
            background: #000;
            letter-spacing: 0.03em;
          }
          .hero-section, .content-wrapper {
            display: block !important;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 1;
          }
          .era-timeline {
            top: 0;
            left: 0;
            width: 80%;
            margin: auto;
            padding: 2rem;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
          }
          .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            filter: contrast(1.1) saturate(1.2);
          }
          .style-card {
            background: rgb(15, 15, 20, 0.8);
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            transform: perspective(1000px) rotateX(0deg);
          }
          .style-card:hover {
            transform: perspective(1000px) rotateX(2deg) translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.9);
            border-color: rgba(255, 255, 255, 0.4);
          }
          .style-description {
            color: #ccc;
            font-weight: 300;
            line-height: 1.6;
            letter-spacing: 0.02em;
          }
          .style-title {
            color: #e6c07b;
            font-weight: bold;
            font-size: 1.8rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(230, 192, 123, 0.3);
            padding-bottom: 0.5rem;
          }
          .style-links a {
            color: #61afef;
            text-decoration: none;
            border: 1px solid rgba(97, 175, 239, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 2px;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
            display: inline-block;
          }
          .style-links a:hover {
            background: rgba(97, 175, 239, 0.1);
            border-color: rgba(97, 175, 239, 0.6);
            transform: translateY(-2px);
          }
          .extra-under { display: block !important; }
          .mermaid {
            background: rgba(20, 20, 25, 0.9);
            color: #ddd;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }
          #css-editor,
          #html-editor {
            font-family: 'Fira Code', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(20, 20, 25, 0.9);
            color: #ddd;
            resize: vertical;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
          }
          .css-editor, .html-editor {
            margin: auto;
            max-width: 800px;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Playfair Display', serif;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
            margin: 0;
            padding: 0;
            background: #000;
            letter-spacing: 0.03em;
          }
          .hero-section, .content-wrapper {
            display: block !important;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 1;
          }
          .era-timeline {
            top: 0;
            left: 0;
            width: 80%;
            margin: auto;
            padding: 2rem;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
          }
          .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            filter: contrast(1.1) saturate(1.2);
          }
          .style-card {
            background: rgba(15, 15, 20, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            transform: perspective(1000px) rotateX(0deg);
          }
          .style-card:hover {
            transform: perspective(1000px) rotateX(2deg) translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.9);
            border-color: rgba(255, 255, 255, 0.4);
          }
          .style-description {
            color: #ccc;
            font-weight: 300;
            line-height: 1.6;
            letter-spacing: 0.02em;
          }
          .style-title {
            color: #e6c07b;
            font-weight: bold;
            font-size: 1.8rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(230, 192, 123, 0.3);
            padding-bottom: 0.5rem;
          }
          .style-links a {
            color: #61afef;
            text-decoration: none;
            border: 1px solid rgba(97, 175, 239, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 2px;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
            display: inline-block;
          }
          .style-links a:hover {
            background: rgba(97, 175, 239, 0.1);
            border-color: rgba(97, 175, 239, 0.6);
            transform: translateY(-2px);
          }
          .extra-under { display: block !important; }
          .mermaid {
            background: rgba(20, 20, 25, 0.9);
            color: #ddd;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }
          #css-editor,
          #html-editor {
            font-family: 'Fira Code', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(20, 20, 25, 0.9);
            color: #ddd;
            resize: vertical;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
          }
          .css-editor, .html-editor {
            margin: auto;
            max-width: 800px;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'brutalist',
          'handle': 'brutalistStyle',
          'name': 'BRUTALIST',
          'lightCss': `
          body {
            font-family: monospace;
            background: white;
            color: black;
            margin: 0;
            padding: 0;
            line-height: 1;
          }
          main {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 2fr));
            gap: 30px;
            justify-content: center;
          }
          .style-card {
            background: white;
            border: 4px solid black;
            border-radius: 0;
            padding: 1rem;
            margin: 0;
            box-shadow: none;
          }
          .style-title {
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: -1px;
            margin-bottom: 20px;
            border-bottom: 4px solid black;
            padding-bottom: 10px;
          }
          .style-description {
            margin: 20px 0;
            text-transform: uppercase;
            font-size: 14px;
          }
          .style-links a {
            display: inline-block;
            background: black;
            color: white;
            padding: 10px 15px;
            margin: 5px 0;
            text-decoration: none;
            text-transform: uppercase;
            border: none;
          }
          .style-links a:hover {
            background: white;
            color: black;
            outline: 4px solid black;
          }
          #css-editor, #html-editor {
            background-color: black;
            color: white;
            border: 4px solid white;
            font-family: monospace;
            border-radius: 0;
            padding: 20px;
            resize: none;
          }
          .css-editor, .html-editor {
            margin: 30px 0;
            width: 100%;
            min-height: 200px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: monospace;
            background: black;
            color: white;
            margin: 0;
            padding: 0;
            line-height: 1;
          }
          main {
            max-width: 100%;
            margin: 0;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
          }
          .style-card {
            background: black;
            border: 4px solid white;
            border-radius: 0;
            padding: 1rem;
            margin: 0;
            box-shadow: none;
            color: white;
          }
          .style-title {
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: -1px;
            margin-bottom: 20px;
            border-bottom: 4px solid white;
            padding-bottom: 10px;
            color: white;
          }
          .style-description {
            margin: 20px 0;
            text-transform: uppercase;
            font-size: 14px;
          }
          .style-links a {
            display: inline-block;
            background: white;
            color: black;
            padding: 10px 15px;
            margin: 5px 0;
            text-decoration: none;
            text-transform: uppercase;
            border: none;
          }
          .style-links a:hover {
            background: black;
            color: white;
            outline: 4px solid white;
          }
          #css-editor, #html-editor {
            background-color: black;
            color: white;
            border: 4px solid white;
            font-family: monospace;
            border-radius: 0;
            padding: 20px;
            resize: none;
          }
          .css-editor, .html-editor {
            margin: 30px 0;
            width: 100%;
            min-height: 200px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'neo-brutalism',
          'handle': 'neo-brutalismStyle',
          'name': 'Neo-Brutalism Design',
          'lightCss': `
          body {
            font-family: 'Arial Black', 'Impact', sans-serif;
            background: #ffde59;
            color: black;
            overflow-x: hidden;
          }
          main {
            position: relative;
            max-width: 90%;
            margin: 2rem auto;
            display: flex;
            flex-wrap: wrap !important;
            gap: 30px;
          }
          .style-card {
            background: white;
            border: 4px solid black;
            border-radius: 0;
            box-shadow: 8px 8px 0 black;
            transform: rotate(-1.5deg);
            transition: all 0.2s ease;
            padding: 25px;
          }
          .style-card:hover {
            transform: rotate(1deg) translateY(-5px);
            box-shadow: 12px 12px 0 black;
          }
          .style-title {
            font-size: 1.8rem;
            text-transform: uppercase;
            color: #ff3366;
            -webkit-text-stroke: 1px black;
          }
          .style-links a {
            display: inline-block;
            background: #00ddff;
            color: black;
            font-weight: bold;
            text-decoration: none;
            padding: 8px 15px;
            margin: 5px;
            border: 3px solid black;
            box-shadow: 4px 4px 0 black;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            background: #ff3366;
            transform: translateY(-3px);
            box-shadow: 6px 6px 0 black;
          }
          #css-editor, #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border: 4px solid black;
            border-radius: 0;
            background-color: white;
            box-shadow: 8px 8px 0 black;
            resize: vertical;
            transform: rotate(-1deg);
          }
          .css-editor, .html-editor {
            margin: 2rem auto;
            min-width: 500px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Arial Black', 'Impact', sans-serif;
            background: #121212;
            color: white;
            overflow-x: hidden;
          }
          main {
            position: relative;
            max-width: 90%;
            margin: 2rem auto;
            display: flex;
            flex-wrap: wrap !important;
            gap: 30px;
          }
          .style-card {
            background: #1a1a1a;
            border: 4px solid #00ddff;
            border-radius: 0;
            box-shadow: 8px 8px 0 #00ddff;
            transform: rotate(-1.5deg);
            transition: all 0.2s ease;
            padding: 25px;
          }
          .style-card:hover {
            transform: rotate(1deg) translateY(-5px);
            box-shadow: 12px 12px 0 #00ddff;
          }
          .style-title {
            font-size: 1.8rem;
            text-transform: uppercase;
            color: #ff3366;
            -webkit-text-stroke: 1px #00ddff;
          }
          .style-links a {
            display: inline-block;
            background: #00ddff;
            color: black;
            font-weight: bold;
            text-decoration: none;
            padding: 8px 15px;
            margin: 5px;
            border: 3px solid white;
            box-shadow: 4px 4px 0 white;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            background: #ff3366;
            transform: translateY(-3px);
            box-shadow: 6px 6px 0 white;
          }
          #css-editor, #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border: 4px solid #00ddff;
            border-radius: 0;
            background-color: #1a1a1a;
            color: white;
            box-shadow: 8px 8px 0 #00ddff;
            resize: vertical;
            transform: rotate(-1deg);
          }
          .css-editor, .html-editor {
            margin: 2rem auto;
            min-width: 500px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'dark-mode',
          'handle': 'dark-modeStyle',
          'name': 'Dark Mode Design',
          'lightCss': `
          body {
            font-family: 'SF Pro Text', 'Roboto', sans-serif;
            background: #121212;
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 8px;
            color: #e0e0e0;
          }
          .style-title {
            color: #bb86fc;
          }
          .style-links a {
            color: #03dac6;
          }
          #css-editor {
            background-color: #2a2a2a;
            color: #e0e0e0;
            border-color: #444;
          }
          /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #2a2a2a;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'SF Pro Text', 'Roboto', sans-serif;
          background: #0a0a0a;
          color: #e0e0e0;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          margin: auto;
          top: 5vh;
          max-width: 1200px;
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: #151515;
          border: 1px solid #333;
          border-radius: 8px;
          color: #e0e0e0;
          box-shadow: 0 0 15px rgba(187, 134, 252, 0.3);
          transition: all 0.3s ease;
        }
        .style-card:hover {
          box-shadow: 0 0 25px rgba(187, 134, 252, 0.5);
        }
        .style-title {
          color: #bb86fc;
          text-shadow: 0 0 8px rgba(187, 134, 252, 0.7);
        }
        .style-links a {
          color: #03dac6;
          text-shadow: 0 0 8px rgba(3, 218, 198, 0.7);
          transition: all 0.3s ease;
        }
        .style-links a:hover {
          color: #5df2e6;
          text-shadow: 0 0 12px rgba(3, 218, 198, 0.9);
        }
        #css-editor {
          background-color: #1a1a1a;
          color: #e0e0e0;
          border: 1px solid #444;
          box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8), 0 0 10px rgba(187, 134, 252, 0.2);
        }
        /* CSS editor styles */
        #css-editor,
        #html-editor {
          font-family: 'Courier New', monospace;
          line-height: 1.5;
          padding: 10px;
          border-radius: 4px;
          margin: 10px 0;
          resize: vertical;
        }
        
        .css-editor {
          margin: auto;
          max-width: 800vw;
          min-width: 500px;
          min-height: 150px;
          text-align: center;
          display: block;
        }
        
        .html-editor {
          margin: auto;
          max-width: calc(100% - 20px);
          min-width: 500px;
          min-height: 500px;
          text-align: center;
          display: block;
        }
        /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'neumorphism',
        'handle': 'neumorphismStyle',
        'name': 'Neumorphism Design',
        'lightCss': `
         body {
            font-family: 'Poppins', sans-serif;
            background: #e0e5ec;
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: #e0e5ec;
            border-radius: 20px;
            box-shadow: 10px 10px 20px #b8bec7, 
                        -10px -10px 20px #ffffff;
            border: none;
            padding: 25px;
            transition: all 0.3s ease;
          }
          .style-card:hover {
            box-shadow: 5px 5px 10px #b8bec7, 
                        -5px -5px 10px #ffffff;
          }
          .style-title {
            color: #555;
            font-weight: 600;
          }
          .style-links a {
            color: #555;
            text-decoration: none;
            padding: 8px 15px;
            margin-right: 10px;
            border-radius: 10px;
            background: #e0e5ec;
            box-shadow: 5px 5px 10px #b8bec7, 
                        -5px -5px 10px #ffffff;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            box-shadow: inset 3px 3px 6px #b8bec7, 
                        inset -3px -3px 6px #ffffff;
          }
          #css-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 20px;
            margin: 15px 0;
            border: none;
            background: #e0e5ec;
            box-shadow: inset 5px 5px 10px #b8bec7, 
                        inset -5px -5px 10px #ffffff;
            resize: vertical;
            color: #555;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Poppins', sans-serif;
            background: #2d3436;
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: #2d3436;
            border-radius: 20px;
            box-shadow: 10px 10px 20px #1a1f20, 
                        -10px -10px 20px #40494c;
            border: none;
            padding: 25px;
            color: #e0e0e0;
            transition: all 0.3s ease;
          }
          .style-card:hover {
            box-shadow: 5px 5px 10px #1a1f20, 
                        -5px -5px 10px #40494c;
          }
          .style-title {
            color: #bb86fc;
            font-weight: 600;
          }
          .style-links a {
            color: #03dac6;
            text-decoration: none;
            padding: 8px 15px;
            margin-right: 10px;
            border-radius: 10px;
            background: #2d3436;
            box-shadow: 5px 5px 10px #1a1f20, 
                        -5px -5px 10px #40494c;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            box-shadow: inset 3px 3px 6px #1a1f20, 
                        inset -3px -3px 6px #40494c;
          }
          #css-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 20px;
            margin: 15px 0;
            border: none;
            background: #2d3436;
            box-shadow: inset 5px 5px 10px #1a1f20, 
                        inset -5px -5px 10px #40494c;
            resize: vertical;
            color: #e0e0e0;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
      },
        {
          'id': 'glassmorphism',
          'handle': 'glassmorphismStyle',
          'name': 'Glassmorphism Design',
          'lightCss': `
          body {
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f0f4f8, #d7e3fc);
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          }
          .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
          }
          .style-title {
            color: #4361ee;
          }
          .style-links a {
            color: #4361ee;
            text-decoration: none;
          }
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 16px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0f172a, #1e293b);
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            color: #e0e0e0;
            transition: all 0.3s ease;
          }
          .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
          }
          .style-title {
            color: #a78bfa;
          }
          .style-links a {
            color: #7dd3fc;
            text-decoration: none;
          }
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 16px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            color: #e0e0e0;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'corporate-memphis',
          'handle': 'corporate-memphisStyle',
          'name': 'Corporate Memphis',
          'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `body {
            font-family: 'Arial', sans-serif;
            background: #121212;
            color: #e0e0e0;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: #1a1a1a;
            border: 3px solid white;
            border-radius: 0;
            box-shadow: 5px 5px 0 white;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'retro-web',
          'handle': 'retro-webStyle',
          'name': 'Retro Web',
          'lightCss': `
          @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Press+Start+2P&family=VT323&family=Share+Tech+Mono&display=swap');

          /* Retro Web - The Ultimate '90s Experience */
          body {
            font-family: 'VT323', 'Courier New', 'Lucida Console', monospace;
            background: #000011;
            background-image:
              radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(120, 255, 198, 0.3) 0%, transparent 50%),
              radial-gradient(white 1px, transparent 1px),
              radial-gradient(#00ff41 0.5px, transparent 1px),
              radial-gradient(#ff0080 0.5px, transparent 1px);
            background-size:
              100% 100%,
              100% 100%,
              100% 100%,
              100px 100px,
              150px 150px,
              200px 200px;
            background-position:
              0 0,
              0 0,
              0 0,
              0 0,
              50px 50px,
              100px 100px;
            color: #00ff41;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            animation: cosmicDrift 30s linear infinite, scanlines 0.1s linear infinite;
            position: relative;
          }

          /* Enhanced starfield animation */
          @keyframes cosmicDrift {
            0% {
              background-position: 0 0, 0 0, 0 0, 0 0, 50px 50px, 100px 100px;
              filter: hue-rotate(0deg);
            }
            25% {
              background-position: 0 0, 0 0, 0 0, 25px 25px, 75px 75px, 125px 125px;
              filter: hue-rotate(90deg);
            }
            50% {
              background-position: 0 0, 0 0, 0 0, 50px 50px, 100px 100px, 150px 150px;
              filter: hue-rotate(180deg);
            }
            75% {
              background-position: 0 0, 0 0, 0 0, 75px 75px, 125px 125px, 175px 175px;
              filter: hue-rotate(270deg);
            }
            100% {
              background-position: 0 0, 0 0, 0 0, 100px 100px, 150px 150px, 200px 200px;
              filter: hue-rotate(360deg);
            }
          }

          /* CRT scanlines effect */
          @keyframes scanlines {
            0% { background-position: 0 0; }
            100% { background-position: 0 2px; }
          }

          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
              0deg,
              transparent,
              transparent 2px,
              rgba(0, 255, 65, 0.03) 2px,
              rgba(0, 255, 65, 0.03) 4px
            );
            pointer-events: none;
            z-index: 1000;
            animation: scanlineMove 0.1s linear infinite;
          }

          @keyframes scanlineMove {
            0% { transform: translateY(0); }
            100% { transform: translateY(2px); }
          }

          /* Matrix rain effect */
          .matrix-rain {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0.1;
          }

          /* Enhanced retro elements */
          #retro-elements {
            display: block !important;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 50;
            pointer-events: none;
          }

          /* Animated starfield overlay */
          .retro-starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
              radial-gradient(2px 2px at 20px 30px, #fff, transparent),
              radial-gradient(2px 2px at 40px 70px, #ff0080, transparent),
              radial-gradient(1px 1px at 90px 40px, #00ff80, transparent),
              radial-gradient(1px 1px at 130px 80px, #0080ff, transparent),
              radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: starTwinkle 4s ease-in-out infinite alternate;
            pointer-events: none;
            z-index: 1;
          }

          @keyframes starTwinkle {
            0% { opacity: 0.3; transform: scale(1); }
            100% { opacity: 0.8; transform: scale(1.1); }
          }

          /* Enhanced marquee with more retro styling */
          .retro-marquee {
            background: linear-gradient(45deg, #ff0080, #8000ff, #0080ff, #00ff80, #ffff00, #ff0080);
            background-size: 600% 600%;
            animation: rainbow 2s ease infinite, pulse 1s ease-in-out infinite alternate;
            color: white;
            font-family: 'Press Start 2P', monospace;
            font-weight: bold;
            text-shadow:
              2px 2px 0 black,
              0 0 10px currentColor,
              0 0 20px currentColor;
            padding: 8px 0;
            border: 3px solid #ffff00;
            border-style: outset;
            box-shadow:
              0 0 20px #ffff00,
              inset 0 0 20px rgba(255, 255, 0, 0.2);
            pointer-events: auto;
            position: relative;
            overflow: hidden;
          }

          .retro-marquee::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shine 3s infinite;
          }

          @keyframes rainbow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
          }

          @keyframes pulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.02); }
          }

          @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
          }

          /* Retro badges styling */
          .retro-badges {
            position: fixed;
            left: 10px;
            top: 100px;
            z-index: 60;
            pointer-events: auto;
          }

          .retro-badge {
            position: absolute;
            background: linear-gradient(45deg, #c0c0c0, #ffffff, #c0c0c0);
            border: 2px outset #c0c0c0;
            padding: 5px;
            margin-bottom: 10px;
            box-shadow: 3px 3px 0 #808080;
            animation: float 3s ease-in-out infinite;
          }

          .badge-1 { top: 0; animation-delay: 0s; }
          .badge-2 { top: 60px; animation-delay: 1s; }
          .badge-3 { top: 120px; animation-delay: 2s; }

          .badge-img {
            width: 80px;
            height: 31px;
            margin-bottom: 5px;
          }

          .netscape-badge {
            background: linear-gradient(45deg, #ff0000, #ffff00);
            border: 1px solid #000000;
          }

          @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
          }

          .badge-text {
            font-family: 'Press Start 2P', monospace;
            font-size: 6px;
            color: #000080;
            text-align: center;
            line-height: 1.2;
            text-shadow: 1px 1px 0 white;
          }

          /* Enhanced blinking elements */
          .retro-blink-container {
            position: fixed;
            top: 70px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            pointer-events: auto;
            z-index: 60;
          }

          .retro-blink {
            background: linear-gradient(45deg, #ff0000, #ff4444);
            color: #ffff00;
            padding: 6px 12px;
            border: 3px outset #ff0000;
            font-family: 'Press Start 2P', monospace;
            font-size: 7px;
            animation: blink 0.8s infinite, glow 2s ease-in-out infinite alternate;
            text-shadow:
              1px 1px 0 black,
              0 0 5px currentColor;
            box-shadow:
              0 0 10px #ff0000,
              inset 0 0 5px rgba(255, 255, 255, 0.2);
            transform: rotate(-1deg);
            cursor: pointer;
          }

          .retro-blink:nth-child(2) {
            animation-delay: 0.2s;
            transform: rotate(1deg);
            background: linear-gradient(45deg, #00ff00, #44ff44);
            border-color: #00ff00;
            box-shadow: 0 0 10px #00ff00, inset 0 0 5px rgba(255, 255, 255, 0.2);
          }

          .retro-blink:nth-child(3) {
            animation-delay: 0.4s;
            transform: rotate(-0.5deg);
            background: linear-gradient(45deg, #0080ff, #4488ff);
            border-color: #0080ff;
            box-shadow: 0 0 10px #0080ff, inset 0 0 5px rgba(255, 255, 255, 0.2);
          }

          .retro-blink:nth-child(4) {
            animation-delay: 0.6s;
            transform: rotate(1.5deg);
            background: linear-gradient(45deg, #ff8000, #ffaa44);
            border-color: #ff8000;
            box-shadow: 0 0 10px #ff8000, inset 0 0 5px rgba(255, 255, 255, 0.2);
          }

          @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
          }

          @keyframes glow {
            0% { box-shadow: 0 0 5px currentColor, inset 0 0 5px rgba(255, 255, 255, 0.2); }
            100% { box-shadow: 0 0 20px currentColor, inset 0 0 10px rgba(255, 255, 255, 0.4); }
          }

          /* Enhanced webring */
          .retro-webring {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #800080, #a000a0, #800080);
            border: 4px ridge #c0c0c0;
            padding: 12px;
            font-family: 'VT323', monospace;
            color: #ffff00;
            pointer-events: auto;
            box-shadow:
              5px 5px 0 #404040,
              0 0 20px rgba(128, 0, 128, 0.5);
            animation: webringPulse 3s ease-in-out infinite;
            min-width: 180px;
          }

          @keyframes webringPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
          }

          .webring-title {
            font-family: 'Press Start 2P', monospace;
            font-weight: bold;
            text-align: center;
            margin-bottom: 3px;
            color: #00ffff;
            font-size: 8px;
            text-shadow:
              1px 1px 0 black,
              0 0 5px currentColor;
            animation: titleGlow 2s ease-in-out infinite alternate;
          }

          @keyframes titleGlow {
            0% { text-shadow: 1px 1px 0 black, 0 0 5px currentColor; }
            100% { text-shadow: 1px 1px 0 black, 0 0 15px currentColor; }
          }

          .webring-subtitle {
            font-size: 10px;
            text-align: center;
            color: #ff80ff;
            margin-bottom: 8px;
            font-style: italic;
          }

          .webring-nav {
            display: flex;
            gap: 3px;
            flex-wrap: wrap;
            justify-content: center;
          }

          .webring-link {
            background: linear-gradient(135deg, #c0c0c0, #ffffff, #c0c0c0);
            color: #000080;
            padding: 4px 8px;
            text-decoration: none;
            border: 2px outset #c0c0c0;
            font-family: 'Press Start 2P', monospace;
            font-size: 6px;
            font-weight: bold;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0 white;
          }

          .webring-link:hover {
            border: 2px inset #c0c0c0;
            background: linear-gradient(135deg, #a0a0a0, #d0d0d0, #a0a0a0);
            transform: translateY(1px);
          }

          .webring-link:active {
            background: linear-gradient(135deg, #808080, #a0a0a0, #808080);
          }

          /* Enhanced guestbook */
          .retro-guestbook {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #008080, #00a0a0, #008080);
            border: 4px ridge #c0c0c0;
            padding: 12px;
            max-width: 280px;
            font-family: 'VT323', monospace;
            font-size: 12px;
            color: white;
            pointer-events: auto;
            box-shadow:
              5px 5px 0 #404040,
              0 0 20px rgba(0, 128, 128, 0.5);
            animation: guestbookFloat 4s ease-in-out infinite;
          }

          @keyframes guestbookFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-3px) rotate(0.5deg); }
          }

          .guestbook-title {
            color: #ffff00;
            font-family: 'Press Start 2P', monospace;
            font-weight: bold;
            text-align: center;
            margin-bottom: 8px;
            font-size: 8px;
            text-shadow:
              1px 1px 0 black,
              0 0 10px currentColor;
            animation: titleFlicker 1.5s ease-in-out infinite alternate;
          }

          @keyframes titleFlicker {
            0% { opacity: 1; }
            100% { opacity: 0.8; }
          }

          .guestbook-entries {
            max-height: 150px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #ffff00 #008080;
          }

          .guestbook-entries::-webkit-scrollbar {
            width: 12px;
          }

          .guestbook-entries::-webkit-scrollbar-track {
            background: #004040;
            border: 1px inset #c0c0c0;
          }

          .guestbook-entries::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #c0c0c0, #ffffff, #c0c0c0);
            border: 1px outset #c0c0c0;
          }

          .guestbook-entry {
            background: linear-gradient(135deg, #ffffff, #f0f0f0, #ffffff);
            color: #000080;
            padding: 8px;
            border: 2px inset #c0c0c0;
            margin-bottom: 6px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            line-height: 1.3;
            box-shadow: inset 1px 1px 0 white, inset -1px -1px 0 #808080;
          }

          .guestbook-entry strong {
            color: #800080;
            font-weight: bold;
          }

          .guestbook-entry em {
            color: #008000;
            font-style: italic;
          }

          .guestbook-entry small {
            color: #666666;
            font-size: 8px;
          }

          /* Enhanced hit counter */
          .retro-hit-counter {
            position: fixed;
            top: 140px;
            right: 20px;
            background: linear-gradient(135deg, #000000, #001100, #000000);
            border: 3px ridge #00ff00;
            padding: 10px;
            font-family: 'Press Start 2P', monospace;
            font-size: 7px;
            color: #00ff00;
            text-align: center;
            pointer-events: auto;
            box-shadow:
              0 0 20px #00ff00,
              inset 0 0 10px rgba(0, 255, 0, 0.1);
            animation: counterGlow 2s ease-in-out infinite alternate;
            min-width: 120px;
          }

          @keyframes counterGlow {
            0% {
              box-shadow: 0 0 10px #00ff00, inset 0 0 5px rgba(0, 255, 0, 0.1);
              transform: scale(1);
            }
            100% {
              box-shadow: 0 0 30px #00ff00, inset 0 0 15px rgba(0, 255, 0, 0.2);
              transform: scale(1.02);
            }
          }

          .counter-title {
            color: #ffff00;
            margin-bottom: 5px;
            text-shadow: 0 0 5px currentColor;
          }

          .counter-display {
            background: #000000;
            border: 2px inset #00ff00;
            padding: 5px;
            margin: 5px 0;
            font-family: 'Share Tech Mono', monospace;
            font-size: 12px;
            letter-spacing: 2px;
            text-shadow: 0 0 10px currentColor;
          }

          .counter-digit {
            display: inline-block;
            background: #001100;
            border: 1px solid #00ff00;
            padding: 2px 4px;
            margin: 0 1px;
            animation: digitFlicker 3s ease-in-out infinite;
          }

          .counter-digit:nth-child(odd) { animation-delay: 0.5s; }
          .counter-digit:nth-child(even) { animation-delay: 1s; }

          @keyframes digitFlicker {
            0%, 90%, 100% { opacity: 1; }
            95% { opacity: 0.7; }
          }

          .counter-text {
            font-size: 6px;
            line-height: 1.2;
            margin: 2px 0;
            color: #80ff80;
          }

          /* Navigation frame simulation */
          .retro-nav-frame {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #c0c0c0);
            border: 3px ridge #c0c0c0;
            padding: 10px;
            width: 120px;
            font-family: 'VT323', monospace;
            pointer-events: auto;
            box-shadow: 5px 5px 0 #808080;
            z-index: 55;
          }

          .nav-title {
            background: linear-gradient(135deg, #000080, #0000a0);
            color: white;
            padding: 4px;
            text-align: center;
            font-family: 'Press Start 2P', monospace;
            font-size: 6px;
            margin-bottom: 8px;
            border: 1px outset #000080;
          }

          .nav-links {
            display: flex;
            flex-direction: column;
            gap: 3px;
          }

          .nav-link {
            background: linear-gradient(135deg, #c0c0c0, #ffffff, #c0c0c0);
            color: #000080;
            padding: 6px 8px;
            text-decoration: none;
            border: 2px outset #c0c0c0;
            font-size: 8px;
            text-align: center;
            transition: all 0.1s ease;
            font-weight: bold;
          }

          .nav-link:hover {
            border: 2px inset #c0c0c0;
            background: linear-gradient(135deg, #a0a0a0, #d0d0d0, #a0a0a0);
          }

          /* Status bar */
          .retro-status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #c0c0c0);
            border-top: 2px ridge #c0c0c0;
            padding: 4px 10px;
            font-family: 'VT323', monospace;
            font-size: 11px;
            color: #000080;
            pointer-events: auto;
            z-index: 60;
            box-shadow: inset 0 2px 0 white, inset 0 -2px 0 #808080;
          }

          #status-text {
            animation: typewriter 4s steps(40) infinite;
            overflow: hidden;
            white-space: nowrap;
          }

          @keyframes typewriter {
            0% { width: 0; }
            50% { width: 100%; }
            100% { width: 0; }
          }

          /* Enhanced main content styling */
          main {
            position: relative;
            max-width: 85%;
            margin: 80px auto 60px auto;
            padding: 25px;
            background:
              linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 20, 40, 0.9)),
              repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 255, 0.03) 2px,
                rgba(0, 255, 255, 0.03) 4px
              );
            border: 4px ridge #00ffff;
            box-shadow:
              0 0 30px #00ffff,
              inset 0 0 30px rgba(0, 255, 255, 0.1),
              0 0 60px rgba(0, 255, 255, 0.3);
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
            backdrop-filter: blur(3px);
            animation: mainGlow 3s ease-in-out infinite alternate;
          }

          @keyframes mainGlow {
            0% {
              box-shadow: 0 0 20px #00ffff, inset 0 0 20px rgba(0, 255, 255, 0.1), 0 0 40px rgba(0, 255, 255, 0.2);
            }
            100% {
              box-shadow: 0 0 40px #00ffff, inset 0 0 40px rgba(0, 255, 255, 0.2), 0 0 80px rgba(0, 255, 255, 0.4);
            }
          }

          /* Enhanced content wrapper */
          .content-wrapper {
            display: block;
            position: relative;
            background:
              linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(40, 0, 40, 0.95)),
              repeating-linear-gradient(
                90deg,
                transparent,
                transparent 1px,
                rgba(255, 0, 255, 0.05) 1px,
                rgba(255, 0, 255, 0.05) 2px
              );
            border: 4px ridge #ff00ff;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow:
              0 0 25px #ff00ff,
              inset 0 0 25px rgba(255, 0, 255, 0.1);
            animation: contentPulse 4s ease-in-out infinite;
          }

          @keyframes contentPulse {
            0%, 100% {
              box-shadow: 0 0 20px #ff00ff, inset 0 0 20px rgba(255, 0, 255, 0.1);
              transform: scale(1);
            }
            50% {
              box-shadow: 0 0 35px #ff00ff, inset 0 0 35px rgba(255, 0, 255, 0.15);
              transform: scale(1.01);
            }
          }

          .header-content {
            max-width: 100%;
            text-align: center;
            position: relative;
          }

          .header-content::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background:
              repeating-conic-gradient(
                from 0deg at 50% 50%,
                transparent 0deg,
                rgba(0, 255, 255, 0.1) 45deg,
                transparent 90deg
              );
            animation: rotate 10s linear infinite;
            z-index: -1;
          }

          @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          .header-content h1 {
            font-family: 'Orbitron', monospace;
            font-size: 2.8rem;
            color: #00ffff;
            text-shadow:
              0 0 5px #00ffff,
              0 0 10px #00ffff,
              0 0 20px #00ffff,
              0 0 40px #00ffff,
              2px 2px 0 #000080;
            animation: titleGlow 2s ease-in-out infinite alternate, titleFloat 4s ease-in-out infinite;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
          }

          @keyframes titleGlow {
            from {
              text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 40px #00ffff, 2px 2px 0 #000080;
              filter: hue-rotate(0deg);
            }
            to {
              text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff, 2px 2px 0 #000080;
              filter: hue-rotate(30deg);
            }
          }

          @keyframes titleFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
          }

          .header-content p {
            font-family: 'Press Start 2P', monospace;
            color: #ffff00;
            font-size: 0.9rem;
            text-shadow:
              1px 1px 0 black,
              0 0 10px currentColor;
            animation: textFlicker 3s ease-in-out infinite;
            line-height: 1.6;
          }

          @keyframes textFlicker {
            0%, 90%, 100% { opacity: 1; }
            95% { opacity: 0.8; }
          }

          /* Enhanced style cards */
          .style-card {
            background:
              linear-gradient(135deg, #000080, #000040, #800080, #400040),
              repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 0, 0.05) 2px,
                rgba(0, 255, 0, 0.05) 4px
              );
            border: 4px ridge #c0c0c0;
            color: #00ff41;
            padding: 18px;
            margin: 12px;
            box-shadow:
              6px 6px 0 #333333,
              0 0 20px rgba(0, 255, 65, 0.3),
              inset 0 0 20px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            animation: cardFloat 6s ease-in-out infinite;
          }

          .style-card:nth-child(odd) { animation-delay: 1s; }
          .style-card:nth-child(even) { animation-delay: 3s; }

          @keyframes cardFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-3px) rotate(0.5deg); }
          }

          .style-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(0, 255, 255, 0.3),
              rgba(255, 0, 255, 0.3),
              rgba(255, 255, 0, 0.3),
              transparent
            );
            animation: scan 4s infinite;
            z-index: 1;
          }

          .style-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
              repeating-linear-gradient(
                0deg,
                transparent,
                transparent 1px,
                rgba(0, 255, 65, 0.1) 1px,
                rgba(0, 255, 65, 0.1) 2px
              );
            pointer-events: none;
            z-index: 1;
          }

          @keyframes scan {
            0% { left: -100%; opacity: 0; }
            50% { opacity: 1; }
            100% { left: 100%; opacity: 0; }
          }

          .style-card:hover {
            border: 4px inset #c0c0c0;
            transform: translateY(3px) scale(1.02);
            box-shadow:
              4px 4px 0 #333333,
              0 0 30px rgba(0, 255, 65, 0.5),
              inset 0 0 30px rgba(255, 255, 255, 0.1);
            animation-play-state: paused;
          }

          .style-card > * {
            position: relative;
            z-index: 2;
          }

          /* Enhanced style elements */
          .style-title {
            font-family: 'Orbitron', monospace;
            color: #ffff00;
            font-weight: 900;
            text-shadow:
              2px 2px 0 black,
              0 0 10px currentColor,
              0 0 20px currentColor;
            font-size: 1.4rem;
            margin-bottom: 8px;
            animation: titlePulse 3s ease-in-out infinite;
            text-transform: uppercase;
            letter-spacing: 1px;
          }

          @keyframes titlePulse {
            0%, 100% {
              text-shadow: 2px 2px 0 black, 0 0 10px currentColor, 0 0 20px currentColor;
              transform: scale(1);
            }
            50% {
              text-shadow: 2px 2px 0 black, 0 0 15px currentColor, 0 0 30px currentColor;
              transform: scale(1.02);
            }
          }

          .style-era {
            color: #00ffff;
            font-family: 'Press Start 2P', monospace;
            font-size: 0.7rem;
            margin-bottom: 12px;
            text-shadow:
              1px 1px 0 black,
              0 0 5px currentColor;
            animation: eraGlow 2s ease-in-out infinite alternate;
            background: rgba(0, 255, 255, 0.1);
            padding: 4px 8px;
            border: 1px solid #00ffff;
            display: inline-block;
          }

          @keyframes eraGlow {
            0% {
              text-shadow: 1px 1px 0 black, 0 0 5px currentColor;
              box-shadow: 0 0 5px #00ffff;
            }
            100% {
              text-shadow: 1px 1px 0 black, 0 0 10px currentColor;
              box-shadow: 0 0 15px #00ffff;
            }
          }

          .style-description {
            color: #e0ffe0;
            font-family: 'VT323', monospace;
            line-height: 1.5;
            margin-bottom: 15px;
            font-size: 1.1rem;
            text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-left: 3px solid #00ff41;
            border-radius: 0 5px 5px 0;
          }

          .style-links {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
          }

          .style-links a {
            background: linear-gradient(135deg, #ff0080, #ff4499, #ff0080);
            color: #ffffff;
            padding: 6px 12px;
            text-decoration: none;
            border: 3px outset #ff0080;
            font-family: 'Press Start 2P', monospace;
            font-size: 0.6rem;
            display: inline-block;
            transition: all 0.2s ease;
            text-shadow: 1px 1px 0 black;
            box-shadow:
              0 0 10px #ff0080,
              inset 0 0 5px rgba(255, 255, 255, 0.2);
            animation: linkGlow 3s ease-in-out infinite;
            position: relative;
            overflow: hidden;
          }

          .style-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.3s ease;
          }

          .style-links a:hover::before {
            left: 100%;
          }

          @keyframes linkGlow {
            0%, 100% { box-shadow: 0 0 5px #ff0080, inset 0 0 5px rgba(255, 255, 255, 0.2); }
            50% { box-shadow: 0 0 20px #ff0080, inset 0 0 10px rgba(255, 255, 255, 0.3); }
          }

          .style-links a:hover {
            border: 3px inset #ff0080;
            background: linear-gradient(135deg, #cc0066, #ff3388, #cc0066);
            transform: translateY(1px) scale(1.05);
            box-shadow:
              0 0 20px #ff0080,
              inset 0 0 10px rgba(255, 255, 255, 0.3);
          }

          .style-links a:active {
            transform: translateY(2px) scale(0.98);
          }

          /* Enhanced timeline */
          .era-timeline {
            background:
              linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 40, 0, 0.95)),
              repeating-linear-gradient(
                90deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 0, 0.05) 2px,
                rgba(0, 255, 0, 0.05) 4px
              );
            border: 4px ridge #00ff00;
            padding: 25px;
            margin: 25px 0;
            box-shadow:
              0 0 30px #00ff00,
              inset 0 0 30px rgba(0, 255, 0, 0.1);
            animation: timelineGlow 4s ease-in-out infinite alternate;
          }

          @keyframes timelineGlow {
            0% {
              box-shadow: 0 0 20px #00ff00, inset 0 0 20px rgba(0, 255, 0, 0.1);
            }
            100% {
              box-shadow: 0 0 50px #00ff00, inset 0 0 50px rgba(0, 255, 0, 0.2);
            }
          }

          .era-timeline h3 {
            font-family: 'Orbitron', monospace;
            color: #00ffff;
            text-align: center;
            text-shadow:
              0 0 10px #00ffff,
              0 0 20px #00ffff,
              2px 2px 0 black;
            font-size: 1.8rem;
            margin-bottom: 20px;
            animation: timelineTitleGlow 3s ease-in-out infinite alternate;
          }

          @keyframes timelineTitleGlow {
            0% {
              text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 2px 2px 0 black;
              transform: scale(1);
            }
            100% {
              text-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff, 2px 2px 0 black;
              transform: scale(1.02);
            }
          }

          .mermaid {
            background:
              linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(40, 40, 0, 0.9)) !important;
            border: 3px solid #ffff00 !important;
            color: #00ff41 !important;
            padding: 15px !important;
            box-shadow:
              0 0 20px #ffff00,
              inset 0 0 20px rgba(255, 255, 0, 0.1) !important;
            animation: mermaidPulse 5s ease-in-out infinite;
          }

          @keyframes mermaidPulse {
            0%, 100% {
              box-shadow: 0 0 15px #ffff00, inset 0 0 15px rgba(255, 255, 0, 0.1);
            }
            50% {
              box-shadow: 0 0 30px #ffff00, inset 0 0 30px rgba(255, 255, 0, 0.2);
            }
          }
          /* Enhanced CSS/HTML editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Share Tech Mono', 'Courier New', monospace;
            line-height: 1.6;
            padding: 15px;
            border: 3px ridge #00ff00;
            margin: 15px 0;
            background:
              linear-gradient(135deg, #000000, #001100),
              repeating-linear-gradient(
                90deg,
                transparent,
                transparent 1px,
                rgba(0, 255, 0, 0.05) 1px,
                rgba(0, 255, 0, 0.05) 2px
              );
            color: #00ff41;
            resize: vertical;
            box-shadow:
              0 0 20px #00ff00,
              inset 0 0 20px rgba(0, 255, 0, 0.1);
            text-shadow: 0 0 5px currentColor;
          }

          .css-editor {
            margin: auto;
            max-width: 90%;
            min-width: 500px;
            min-height: 200px;
            text-align: left;
            display: block;
          }

          .html-editor {
            margin: auto;
            max-width: 90%;
            min-width: 500px;
            min-height: 300px;
            text-align: left;
            display: block;
          }

          /* Enhanced interactive styles */
          .style-card {
            cursor: pointer;
            transition: all 0.3s ease;
          }

          /* Enhanced tooltips */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.3s ease;
            padding: 8px 12px;
            color: #00ff41;
            border: 2px solid #00ff41;
            background: rgba(0, 0, 0, 0.9);
            font-family: 'Press Start 2P', monospace;
            font-size: 8px;
            pointer-events: none;
            z-index: 100;
            white-space: nowrap;
            transform: translateY(10px);
            box-shadow: 0 0 20px #00ff41;
          }

          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }

          /* Responsive adjustments */
          @media (max-width: 768px) {
            .retro-nav-frame,
            .retro-badges {
              display: none;
            }

            .retro-blink-container {
              top: 120px;
              right: 10px;
            }

            .retro-guestbook {
              bottom: 60px;
              right: 10px;
              max-width: 200px;
            }

            .retro-hit-counter {
              top: 180px;
              right: 10px;
            }

            main {
              margin: 60px auto 80px auto;
              padding: 15px;
            }

            .header-content h1 {
              font-size: 2rem;
            }
          }
        `,
        'darkCss': `
          @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Press+Start+2P&family=VT323&family=Share+Tech+Mono&display=swap');

          /* Retro Web - Dark Mode: Terminal/DOS Aesthetic */
          body {
            font-family: 'VT323', 'Courier New', 'Lucida Console', monospace;
            background: #000000;
            background-image:
              radial-gradient(circle at 30% 70%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
              radial-gradient(#00ff00 0.5px, transparent 1px),
              radial-gradient(#ffaa00 0.3px, transparent 1px);
            background-size:
              100% 100%,
              100% 100%,
              80px 80px,
              120px 120px;
            background-position:
              0 0,
              0 0,
              0 0,
              40px 40px;
            color: #00ff00;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            animation: terminalFlicker 0.15s linear infinite, cosmicDriftDark 25s linear infinite;
            position: relative;
          }

          @keyframes terminalFlicker {
            0%, 98%, 100% { opacity: 1; }
            99% { opacity: 0.98; }
          }

          @keyframes cosmicDriftDark {
            0% {
              background-position: 0 0, 0 0, 0 0, 40px 40px;
              filter: hue-rotate(0deg);
            }
            25% {
              background-position: 0 0, 0 0, 20px 20px, 60px 60px;
              filter: hue-rotate(90deg);
            }
            50% {
              background-position: 0 0, 0 0, 40px 40px, 80px 80px;
              filter: hue-rotate(180deg);
            }
            75% {
              background-position: 0 0, 0 0, 60px 60px, 100px 100px;
              filter: hue-rotate(270deg);
            }
            100% {
              background-position: 0 0, 0 0, 80px 80px, 120px 120px;
              filter: hue-rotate(360deg);
            }
          }

          /* Terminal scanlines */
          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
              0deg,
              transparent,
              transparent 2px,
              rgba(0, 255, 0, 0.02) 2px,
              rgba(0, 255, 0, 0.02) 4px
            );
            pointer-events: none;
            z-index: 1000;
            animation: scanlineMoveDark 0.1s linear infinite;
          }

          @keyframes scanlineMoveDark {
            0% { transform: translateY(0); }
            100% { transform: translateY(2px); }
          }

          /* Dark mode retro elements */
          #retro-elements {
            display: block !important;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 50;
            pointer-events: none;
          }

          .retro-starfield {
            background:
              radial-gradient(1px 1px at 25px 35px, #00ff00, transparent),
              radial-gradient(1px 1px at 45px 75px, #ffaa00, transparent),
              radial-gradient(0.5px 0.5px at 95px 45px, #00ff00, transparent),
              radial-gradient(0.5px 0.5px at 135px 85px, #ffaa00, transparent);
            background-size: 150px 100px;
            animation: starTwinkleDark 3s ease-in-out infinite alternate;
          }

          @keyframes starTwinkleDark {
            0% { opacity: 0.4; }
            100% { opacity: 0.7; }
          }

          .retro-marquee {
            background: linear-gradient(45deg, #00ff00, #ffaa00, #00ff00);
            background-size: 400% 400%;
            animation: rainbowDark 3s ease infinite;
            color: #000000;
            font-family: 'Press Start 2P', monospace;
            font-weight: bold;
            text-shadow: none;
            padding: 8px 0;
            border: 3px solid #00ff00;
            border-style: outset;
            box-shadow: 0 0 20px #00ff00;
            pointer-events: auto;
          }

          @keyframes rainbowDark {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
          }
          /* Dark mode main content and elements */
          main {
            position: relative;
            max-width: 85%;
            margin: 80px auto 60px auto;
            padding: 25px;
            background:
              linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 40, 0, 0.95)),
              repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 0, 0.03) 2px,
                rgba(0, 255, 0, 0.03) 4px
              );
            border: 4px ridge #00ff00;
            box-shadow:
              0 0 30px #00ff00,
              inset 0 0 30px rgba(0, 255, 0, 0.1);
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
            backdrop-filter: blur(2px);
            animation: mainGlowDark 4s ease-in-out infinite alternate;
          }

          @keyframes mainGlowDark {
            0% {
              box-shadow: 0 0 20px #00ff00, inset 0 0 20px rgba(0, 255, 0, 0.1);
            }
            100% {
              box-shadow: 0 0 50px #00ff00, inset 0 0 50px rgba(0, 255, 0, 0.2);
            }
          }

          .style-card {
            background:
              linear-gradient(135deg, #001100, #000000, #002200),
              repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(0, 255, 0, 0.05) 2px,
                rgba(0, 255, 0, 0.05) 4px
              );
            border: 4px ridge #00ff00;
            color: #00ff00;
            padding: 18px;
            margin: 12px;
            box-shadow:
              6px 6px 0 #004400,
              0 0 20px rgba(0, 255, 0, 0.3);
            transition: all 0.3s ease;
            animation: cardFloatDark 5s ease-in-out infinite;
          }

          @keyframes cardFloatDark {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-2px); }
          }

          .style-card:hover {
            border: 4px inset #00ff00;
            transform: translateY(2px) scale(1.02);
            box-shadow:
              4px 4px 0 #004400,
              0 0 30px rgba(0, 255, 0, 0.5);
          }

          .style-title {
            color: #ffaa00;
            font-family: 'Orbitron', monospace;
            text-shadow:
              1px 1px 0 black,
              0 0 10px currentColor;
          }

          .style-era {
            color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
          }

          .style-description {
            color: #ccffcc;
            background: rgba(0, 0, 0, 0.5);
            border-left: 3px solid #00ff00;
          }

          .style-links a {
            background: linear-gradient(135deg, #ffaa00, #ff8800);
            border: 3px outset #ffaa00;
            box-shadow: 0 0 10px #ffaa00;
          }

          .style-links a:hover {
            border: 3px inset #ffaa00;
            background: linear-gradient(135deg, #cc8800, #ffaa00);
            box-shadow: 0 0 20px #ffaa00;
          }

          /* Dark mode editors */
          #css-editor,
          #html-editor {
            font-family: 'Share Tech Mono', 'Courier New', monospace;
            background: #000000;
            color: #00ff00;
            border: 3px ridge #00ff00;
            box-shadow: 0 0 20px #00ff00;
            text-shadow: 0 0 5px currentColor;
          }

          .era-timeline {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 40, 0, 0.95));
            border: 4px ridge #00ff00;
            box-shadow: 0 0 30px #00ff00;
          }

          .mermaid {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 40, 0, 0.9)) !important;
            border: 3px solid #00ff00 !important;
            color: #00ff00 !important;
            box-shadow: 0 0 20px #00ff00 !important;
          }

          /* Dark mode retro elements styling */
          .retro-blink {
            background: linear-gradient(45deg, #00ff00, #44ff44);
            color: #000000;
            border: 3px outset #00ff00;
            box-shadow: 0 0 10px #00ff00;
          }

          .retro-webring {
            background: linear-gradient(135deg, #004400, #006600);
            border: 4px ridge #00ff00;
            color: #ffaa00;
            box-shadow: 5px 5px 0 #002200, 0 0 20px #00ff00;
          }

          .retro-guestbook {
            background: linear-gradient(135deg, #002200, #004400);
            border: 4px ridge #00ff00;
            box-shadow: 5px 5px 0 #001100, 0 0 20px #00ff00;
          }

          .retro-hit-counter {
            background: linear-gradient(135deg, #000000, #001100);
            border: 3px ridge #00ff00;
            box-shadow: 0 0 20px #00ff00;
          }

          .retro-nav-frame {
            background: linear-gradient(135deg, #004400, #006600);
            border: 3px ridge #00ff00;
            box-shadow: 5px 5px 0 #002200;
          }

          .retro-status-bar {
            background: linear-gradient(135deg, #004400, #006600);
            border-top: 2px ridge #00ff00;
            color: #ffaa00;
          }

          .content-wrapper {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 40, 0, 0.95));
            border: 4px ridge #00ff00;
            box-shadow: 0 0 25px #00ff00;
          }

          .header-content h1 {
            color: #ffaa00;
            text-shadow:
              0 0 10px #ffaa00,
              0 0 20px #ffaa00,
              2px 2px 0 #000000;
          }

          .header-content p {
            color: #00ff00;
            text-shadow: 1px 1px 0 black, 0 0 10px currentColor;
          }
        `
        },
        {
          'id': 'y2k-web',
          'handle': 'y2k-webStyle',
          'name': 'Y2K Web',
          'lightCss': `
          @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

          body {
            font-family: 'Exo 2', 'Arial', sans-serif;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5, #ffbe0b);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
          }

          @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
          }

          /* Y2K Grid Background Overlay */
          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
            animation: gridPulse 4s ease-in-out infinite;
          }

          @keyframes gridPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
          }

          /* Floating geometric shapes */
          body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
              radial-gradient(circle at 20% 20%, rgba(255, 0, 110, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(131, 56, 236, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 60%, rgba(6, 255, 165, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
            animation: floatShapes 12s ease-in-out infinite;
          }

          @keyframes floatShapes {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
          }

          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 10;
          }

          .style-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border: 2px solid transparent;
            background-clip: padding-box;
            border-radius: 15px;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.5),
              0 0 20px rgba(255,255,255,0.2);
            transform: perspective(1000px) rotateX(5deg) rotateY(-5deg);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
          }

          .style-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5);
            border-radius: 15px;
            padding: 2px;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            z-index: -1;
          }
          /* Y2K Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          }

          .style-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-10px) scale(1.02);
            box-shadow:
              0 20px 40px rgba(0,0,0,0.4),
              inset 0 1px 0 rgba(255,255,255,0.8),
              0 0 30px rgba(255,255,255,0.4),
              0 0 60px rgba(131, 56, 236, 0.3);
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.8));
          }

          .style-card:hover::before {
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5, #ffbe0b);
            animation: borderGlow 2s ease-in-out infinite;
          }

          @keyframes borderGlow {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
          }

          /* Y2K Typography Styles */
          .style-title {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: 1.4rem;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(131, 56, 236, 0.5);
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
          }

          .style-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #ff006e, #8338ec, #3a86ff);
            border-radius: 1px;
            transform: scaleX(0);
            transition: transform 0.3s ease;
          }

          .style-card:hover .style-title::after {
            transform: scaleX(1);
          }

          .style-era {
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            color: #8338ec;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            background: rgba(131, 56, 236, 0.1);
            padding: 4px 8px;
            border-radius: 20px;
            display: inline-block;
            border: 1px solid rgba(131, 56, 236, 0.3);
          }

          .style-description {
            font-family: 'Exo 2', sans-serif;
            color: #333;
            line-height: 1.6;
            margin-bottom: 12px;
          }
          /* Y2K Header Content Styling */
          .content-wrapper {
            display: block;
            position: relative;
            left: 0;
            top: 0;
            z-index: 15;
          }

          .header-content {
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 2rem;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.3);
          }

          .header-content h1 {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: clamp(2rem, 5vw, 4rem);
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5, #ffbe0b);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientText 4s ease infinite;
            text-shadow: 0 0 30px rgba(255,255,255,0.5);
            margin-bottom: 1rem;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 3px;
          }

          @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
          }

          .header-content p {
            font-family: 'Exo 2', sans-serif;
            font-size: clamp(1rem, 2vw, 1.5rem);
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
          }

          /* Y2K Chrome Button Effects */
          .style-links a {
            display: inline-block;
            padding: 8px 16px;
            margin: 4px;
            background: linear-gradient(145deg, #e6e6e6, #ffffff);
            border: 1px solid #ccc;
            border-radius: 20px;
            color: #333;
            text-decoration: none;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            box-shadow:
              0 4px 8px rgba(0,0,0,0.1),
              inset 0 1px 0 rgba(255,255,255,0.8),
              inset 0 -1px 0 rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
          }

          .style-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            transition: left 0.5s ease;
          }

          .style-links a:hover {
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            box-shadow:
              0 6px 12px rgba(0,0,0,0.15),
              inset 0 1px 0 rgba(255,255,255,0.9),
              inset 0 -1px 0 rgba(0,0,0,0.1),
              0 0 20px rgba(131, 56, 236, 0.3);
            transform: translateY(-2px);
          }

          .style-links a:hover::before {
            left: 100%;
          }

          .style-links a:active {
            transform: translateY(0px);
            box-shadow:
              0 2px 4px rgba(0,0,0,0.2),
              inset 0 1px 0 rgba(255,255,255,0.7),
              inset 0 -1px 0 rgba(0,0,0,0.2);
          }

          /* Y2K Tooltip Effects */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            padding: 8px 12px;
            color: #fff;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(131, 56, 236, 0.9), rgba(255, 0, 110, 0.9));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            pointer-events: none;
            z-index: 100;
            white-space: nowrap;
            transform: translateY(10px) scale(0.8);
            font-family: 'Exo 2', sans-serif;
            font-size: 0.8rem;
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
          }

          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
          /* Y2K Timeline Styling */
          .era-timeline {
            display: block !important;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.2);
            opacity: 0.95;
            z-index: 10;
          }

          .mermaid {
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)) !important;
            backdrop-filter: blur(15px) !important;
            border: 1px solid rgba(255,255,255,0.2) !important;
            border-radius: 15px !important;
            color: #333 !important;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2) !important;
          }

          /* Y2K CSS/HTML Editor Styling */
          #css-editor,
          #html-editor {
            font-family: 'Rajdhani', 'Courier New', monospace;
            line-height: 1.6;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid transparent;
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            resize: vertical;
            color: #333;
            box-shadow:
              0 8px 20px rgba(0,0,0,0.2),
              inset 0 1px 0 rgba(255,255,255,0.5);
            transition: all 0.3s ease;
          }

          #css-editor:focus,
          #html-editor:focus {
            outline: none;
            box-shadow:
              0 8px 20px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.6),
              0 0 20px rgba(131, 56, 236, 0.4);
            border: 2px solid rgba(131, 56, 236, 0.5);
          }

          .css-editor,
          .html-editor {
            margin: auto;
            max-width: 90%;
            min-width: 500px;
            min-height: 200px;
            text-align: center;
            display: block;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 2rem;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.2);
          }

          .css-editor h2,
          .html-editor h2 {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
          }

          /* Y2K Special Elements */
          #y2k-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
          }

          /* Chrome Orbs */
          .y2k-orbs {
            position: absolute;
            width: 100%;
            height: 100%;
          }

          .chrome-orb {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #e8e8e8, #ffffff, #d0d0d0);
            box-shadow:
              0 0 20px rgba(255,255,255,0.5),
              inset 0 0 20px rgba(0,0,0,0.1),
              0 0 40px rgba(131, 56, 236, 0.3);
            animation: floatOrb 8s ease-in-out infinite;
          }

          .orb-1 {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
            animation-duration: 6s;
          }

          .orb-2 {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
            animation-duration: 8s;
          }

          .orb-3 {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
            animation-duration: 7s;
          }

          .orb-4 {
            top: 40%;
            right: 30%;
            animation-delay: 1s;
            animation-duration: 9s;
          }

          @keyframes floatOrb {
            0%, 100% {
              transform: translateY(0px) translateX(0px) scale(1);
              opacity: 0.7;
            }
            25% {
              transform: translateY(-20px) translateX(10px) scale(1.1);
              opacity: 0.9;
            }
            50% {
              transform: translateY(10px) translateX(-15px) scale(0.9);
              opacity: 0.6;
            }
            75% {
              transform: translateY(-10px) translateX(20px) scale(1.05);
              opacity: 0.8;
            }
          }

          /* Holographic Text Overlay */
          .holographic-overlay {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0.3;
          }

          .holo-text {
            font-family: 'Orbitron', monospace;
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5, #ffbe0b);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: holoShift 3s ease-in-out infinite;
            text-transform: uppercase;
            letter-spacing: 8px;
            margin: -20px 0;
            transform: perspective(500px) rotateX(45deg);
          }

          .holo-text:nth-child(2) {
            animation-delay: 1s;
            font-size: 3rem;
          }

          .holo-text:nth-child(3) {
            animation-delay: 2s;
            font-size: 2rem;
          }

          @keyframes holoShift {
            0%, 100% {
              background-position: 0% 50%;
              opacity: 0.2;
              transform: perspective(500px) rotateX(45deg) translateZ(0px);
            }
            50% {
              background-position: 100% 50%;
              opacity: 0.4;
              transform: perspective(500px) rotateX(45deg) translateZ(20px);
            }
          }

          /* Floating UI Panels */
          .floating-panels {
            position: absolute;
            width: 100%;
            height: 100%;
          }

          .ui-panel {
            position: absolute;
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 15px;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.3);
            animation: panelFloat 6s ease-in-out infinite;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
          }

          .ui-panel:hover {
            transform: scale(1.05);
            box-shadow:
              0 12px 40px rgba(0,0,0,0.4),
              inset 0 1px 0 rgba(255,255,255,0.4),
              0 0 30px rgba(131, 56, 236, 0.4);
          }

          .panel-1 {
            top: 15%;
            right: 5%;
            width: 200px;
            animation-delay: 0s;
          }

          .panel-2 {
            bottom: 20%;
            left: 5%;
            width: 180px;
            animation-delay: 3s;
          }

          @keyframes panelFloat {
            0%, 100% {
              transform: translateY(0px) rotateY(0deg);
              opacity: 0.8;
            }
            50% {
              transform: translateY(-10px) rotateY(5deg);
              opacity: 0.9;
            }
          }

          .panel-header {
            font-family: 'Orbitron', monospace;
            font-size: 0.8rem;
            font-weight: 700;
            color: #8338ec;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
            text-align: center;
            border-bottom: 1px solid rgba(131, 56, 236, 0.3);
            padding-bottom: 5px;
          }

          .panel-content {
            font-family: 'Exo 2', sans-serif;
            font-size: 0.7rem;
            color: #333;
          }

          .status-bar {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 8px;
          }

          .status-bar span {
            font-weight: 600;
            min-width: 35px;
            font-size: 0.6rem;
          }

          .progress-bar {
            flex: 1;
            height: 8px;
            background: rgba(0,0,0,0.2);
            border-radius: 4px;
            overflow: hidden;
          }

          .progress {
            height: 100%;
            background: linear-gradient(90deg, #ff006e, #8338ec, #3a86ff);
            border-radius: 4px;
            animation: progressPulse 2s ease-in-out infinite;
          }

          @keyframes progressPulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
          }

          .countdown-display {
            display: flex;
            justify-content: space-between;
            gap: 5px;
          }

          .countdown-unit {
            text-align: center;
            flex: 1;
          }

          .countdown-number {
            display: block;
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff006e, #8338ec);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .countdown-label {
            display: block;
            font-size: 0.5rem;
            color: #666;
            margin-top: 2px;
          }

          /* Particle System */
          .particle-system {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
          }

          /* Cursor Trail */
          .cursor-trail {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(131, 56, 236, 0.8), transparent);
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        `,
        'darkCss': `
          @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

          body {
            font-family: 'Exo 2', 'Arial', sans-serif;
            background: linear-gradient(45deg, #0a0a0a, #1a0033, #000066, #003300, #330000);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
          }

          @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
          }

          /* Dark Y2K Grid Background Overlay */
          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
              linear-gradient(rgba(0,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
            animation: gridPulse 4s ease-in-out infinite;
          }

          @keyframes gridPulse {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.5; }
          }

          /* Dark floating geometric shapes */
          body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
              radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.2) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.2) 0%, transparent 50%),
              radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
            animation: floatShapes 12s ease-in-out infinite;
          }

          @keyframes floatShapes {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
          }
          /* Dark Y2K Main Content */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 10;
          }

          /* Dark Y2K Style Cards */
          .style-card {
            background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(20,20,20,0.6));
            border: 2px solid transparent;
            background-clip: padding-box;
            border-radius: 15px;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.6),
              inset 0 1px 0 rgba(255,255,255,0.1),
              0 0 20px rgba(0,255,255,0.2);
            transform: perspective(1000px) rotateX(5deg) rotateY(-5deg);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
          }

          .style-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00);
            border-radius: 15px;
            padding: 2px;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            z-index: -1;
          }

          .style-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-10px) scale(1.02);
            box-shadow:
              0 20px 40px rgba(0,0,0,0.7),
              inset 0 1px 0 rgba(255,255,255,0.2),
              0 0 30px rgba(0,255,255,0.4),
              0 0 60px rgba(255, 0, 255, 0.3);
            background: linear-gradient(135deg, rgba(10,10,10,0.9), rgba(30,30,30,0.7));
          }

          .style-title {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: 1.4rem;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
          }

          .style-era {
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            color: #00ffff;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            background: rgba(0, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 20px;
            display: inline-block;
            border: 1px solid rgba(0, 255, 255, 0.3);
          }

          .style-description {
            font-family: 'Exo 2', sans-serif;
            color: #e0e0e0;
            line-height: 1.6;
            margin-bottom: 12px;
          }

          .style-links a {
            display: inline-block;
            padding: 8px 16px;
            margin: 4px;
            background: linear-gradient(145deg, #333, #555);
            border: 1px solid #666;
            border-radius: 20px;
            color: #00ffff;
            text-decoration: none;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            box-shadow:
              0 4px 8px rgba(0,0,0,0.3),
              inset 0 1px 0 rgba(255,255,255,0.1),
              inset 0 -1px 0 rgba(0,0,0,0.3);
            transition: all 0.2s ease;
          }

          .style-links a:hover {
            background: linear-gradient(145deg, #444, #666);
            box-shadow:
              0 6px 12px rgba(0,0,0,0.4),
              inset 0 1px 0 rgba(255,255,255,0.2),
              0 0 20px rgba(0, 255, 255, 0.4);
            color: #ffffff;
            transform: translateY(-2px);
          }

          /* Dark Y2K Editors */
          #css-editor,
          #html-editor {
            background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(20,20,20,0.6));
            color: #00ffff;
            border: 2px solid rgba(0, 255, 255, 0.3);
            box-shadow:
              0 8px 20px rgba(0,0,0,0.5),
              inset 0 1px 0 rgba(255,255,255,0.1),
              0 0 20px rgba(0, 255, 255, 0.2);
            font-family: 'Rajdhani', 'Courier New', monospace;
          }
          /* Dark Y2K Header Content */
          .content-wrapper {
            display: block;
            position: relative;
            left: 0;
            top: 0;
            z-index: 15;
          }

          .header-content {
            background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(20,20,20,0.5));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0,255,255,0.2);
            border-radius: 20px;
            padding: 2rem;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.6),
              inset 0 1px 0 rgba(255,255,255,0.1);
          }

          .header-content h1 {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: clamp(2rem, 5vw, 4rem);
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00, #ff0080);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientText 4s ease infinite;
            text-shadow: 0 0 30px rgba(0,255,255,0.5);
            margin-bottom: 1rem;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 3px;
          }

          .header-content p {
            font-family: 'Exo 2', sans-serif;
            font-size: clamp(1rem, 2vw, 1.5rem);
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.5);
          }

          /* Dark Y2K Timeline */
          .era-timeline {
            display: block !important;
            background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(20,20,20,0.5));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0,255,255,0.2);
            border-radius: 20px;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.6),
              inset 0 1px 0 rgba(255,255,255,0.1);
            opacity: 0.95;
            z-index: 10;
          }

          .mermaid {
            background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(20,20,20,0.5)) !important;
            backdrop-filter: blur(15px) !important;
            border: 1px solid rgba(0,255,255,0.2) !important;
            border-radius: 15px !important;
            color: #e0e0e0 !important;
            box-shadow: 0 8px 20px rgba(0,0,0,0.4) !important;
          }

          /* Dark Y2K Editor Containers */
          .css-editor,
          .html-editor {
            margin: auto;
            max-width: 90%;
            min-width: 500px;
            min-height: 200px;
            text-align: center;
            display: block;
            background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(20,20,20,0.5));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0,255,255,0.2);
            border-radius: 20px;
            padding: 2rem;
            box-shadow:
              0 8px 32px rgba(0,0,0,0.6),
              inset 0 1px 0 rgba(255,255,255,0.1);
          }

          .css-editor h2,
          .html-editor h2 {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
          }

          /* Dark Y2K Tooltips */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            padding: 8px 12px;
            color: #fff;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.9), rgba(255, 0, 255, 0.9));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            pointer-events: none;
            z-index: 100;
            white-space: nowrap;
            transform: translateY(10px) scale(0.8);
            font-family: 'Exo 2', sans-serif;
            font-size: 0.8rem;
            box-shadow: 0 8px 20px rgba(0,0,0,0.5);
          }

          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        `
        },
        {
          'id': 'webgl-3d',
          'handle': 'webgl-3dStyle',
          'name': 'WebGL & 3D Design',
          'lightCss': `
          /* WebGL & 3D Theme - Light Mode */
          body {
            font-family: 'Orbitron', 'Arial', monospace;
            background: linear-gradient(135deg,
              #0a0a0a 0%,
              #1a1a2e 25%,
              #16213e 50%,
              #0f3460 75%,
              #533483 100%);
            color: #e0e6ed;
            overflow-x: hidden;
            perspective: 1000px;
            min-height: 100vh;
          }

          /* 3D Scene Container */
          .webgl-scene-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: -2;
            background: radial-gradient(ellipse at center,
              rgba(83, 52, 131, 0.3) 0%,
              rgba(15, 52, 96, 0.5) 50%,
              rgba(10, 10, 10, 0.8) 100%);
          }

          .webgl-canvas {
            width: 100%;
            height: 100%;
            display: block;
          }

          /* HUD Elements */
          .scene-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
          }

          .hud-element {
            position: absolute;
            background: rgba(0, 20, 40, 0.8);
            border: 1px solid #00ffff;
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #00ffff;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            pointer-events: auto;
          }

          .hud-top-left {
            top: 20px;
            left: 20px;
          }

          .hud-top-right {
            top: 20px;
            right: 20px;
          }

          .hud-title {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }

          .stat-line, .control-hint {
            margin: 4px 0;
            color: #00ffff;
          }

          /* Main Content */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 10;
          }

          /* 3D Style Cards */
          .style-card {
            background: linear-gradient(145deg,
              rgba(20, 30, 48, 0.9),
              rgba(30, 40, 60, 0.9));
            border: 2px solid rgba(0, 255, 255, 0.6);
            border-radius: 12px;
            box-shadow:
              0 8px 32px rgba(0, 0, 0, 0.3),
              0 0 0 1px rgba(255, 255, 255, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: perspective(1000px) rotateX(5deg) rotateY(-5deg);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
          }

          .style-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
              transparent,
              rgba(0, 255, 255, 0.2),
              transparent);
            transition: left 0.5s;
          }

          .style-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(20px);
            box-shadow:
              0 20px 40px rgba(0, 255, 255, 0.3),
              0 0 0 1px rgba(0, 255, 255, 0.5),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-color: rgba(0, 255, 255, 1);
          }

          .style-card:hover::before {
            left: 100%;
          }

          .style-title {
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
          }

          .style-description {
            color: #b0c4de;
          }

          .style-links a {
            color: #87ceeb;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
          }

          .style-links a:hover {
            color: #00ffff;
            border-bottom-color: #00ffff;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
          }

          /* Floating 3D Panels */
          .floating-3d-panels {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 100;
            display: none; /* Hidden by default, shown when WebGL theme is active */
          }

          .panel-3d {
            background: linear-gradient(145deg,
              rgba(15, 25, 40, 0.95),
              rgba(25, 35, 50, 0.95));
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            backdrop-filter: blur(15px);
            box-shadow:
              0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform-style: preserve-3d;
            transition: all 0.3s ease;
            min-width: 200px;
          }

          .panel-3d:hover {
            transform: translateZ(10px);
            box-shadow:
              0 12px 40px rgba(0, 255, 255, 0.2),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.8);
          }

          .panel-header {
            color: #00ffff;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
            text-align: center;
            font-size: 12px;
          }

          .geo-btn, .mat-btn {
            background: linear-gradient(145deg,
              rgba(0, 255, 255, 0.1),
              rgba(0, 255, 255, 0.05));
            border: 1px solid rgba(0, 255, 255, 0.3);
            color: #00ffff;
            padding: 8px 12px;
            margin: 4px 2px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .geo-btn:hover, .mat-btn:hover {
            background: linear-gradient(145deg,
              rgba(0, 255, 255, 0.3),
              rgba(0, 255, 255, 0.2));
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
            transform: translateY(-2px);
          }

          .geo-btn.active, .mat-btn.active {
            background: linear-gradient(145deg,
              rgba(0, 255, 255, 0.4),
              rgba(0, 255, 255, 0.3));
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.6);
          }

          /* Lighting Controls */
          .light-slider {
            margin: 10px 0;
          }

          .light-slider label {
            display: block;
            color: #b0c4de;
            font-size: 10px;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .slider {
            width: 100%;
            height: 4px;
            border-radius: 2px;
            background: rgba(0, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
          }

          .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #00ffff;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
          }

          .slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #00ffff;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
          }

          /* Particle Field */
          .particle-field {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: -1;
            pointer-events: none;
            display: none; /* Hidden by default, shown when WebGL theme is active */
          }

          /* Navigation Cube */
          .nav-cube-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 100;
            display: none; /* Hidden by default, shown when WebGL theme is active */
          }

          .nav-cube {
            width: 60px;
            height: 60px;
            position: relative;
            transform-style: preserve-3d;
            transform: rotateX(-15deg) rotateY(15deg);
            animation: cubeRotate 10s infinite linear;
          }

          .cube-face {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #00ffff;
            font-weight: bold;
            backdrop-filter: blur(10px);
          }

          .cube-face.front { transform: rotateY(0deg) translateZ(30px); }
          .cube-face.back { transform: rotateY(180deg) translateZ(30px); }
          .cube-face.right { transform: rotateY(90deg) translateZ(30px); }
          .cube-face.left { transform: rotateY(-90deg) translateZ(30px); }
          .cube-face.top { transform: rotateX(90deg) translateZ(30px); }
          .cube-face.bottom { transform: rotateX(-90deg) translateZ(30px); }

          @keyframes cubeRotate {
            0% { transform: rotateX(-15deg) rotateY(15deg); }
            25% { transform: rotateX(-15deg) rotateY(105deg); }
            50% { transform: rotateX(-15deg) rotateY(195deg); }
            75% { transform: rotateX(-15deg) rotateY(285deg); }
            100% { transform: rotateX(-15deg) rotateY(375deg); }
          }

          /* Holographic UI Elements */
          .holographic-ui {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            pointer-events: none;
            display: none; /* Hidden by default, shown when WebGL theme is active */
          }

          .holo-ring {
            position: absolute;
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            animation: holoRotate 20s infinite linear;
          }

          .ring-1 {
            width: 300px;
            height: 300px;
            top: -150px;
            left: -150px;
            animation-duration: 20s;
          }

          .ring-2 {
            width: 500px;
            height: 500px;
            top: -250px;
            left: -250px;
            animation-duration: 30s;
            animation-direction: reverse;
          }

          .ring-3 {
            width: 700px;
            height: 700px;
            top: -350px;
            left: -350px;
            animation-duration: 40s;
          }

          @keyframes holoRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          .holo-grid {
            position: absolute;
            width: 800px;
            height: 800px;
            top: -400px;
            left: -400px;
            background-image:
              linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridPulse 4s infinite ease-in-out;
          }

          @keyframes gridPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
          }

          /* Model Showcase */
          .model-showcase {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            display: none; /* Hidden by default, shown when WebGL theme is active */
          }

          .model-viewer {
            background: rgba(15, 25, 40, 0.9);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(15px);
            width: 200px;
          }

          .wireframe-cube {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            border: 1px solid #00ffff;
            position: relative;
            transform-style: preserve-3d;
            transform: rotateX(20deg) rotateY(20deg);
            animation: wireframeRotate 6s infinite linear;
          }

          .wireframe-cube::before,
          .wireframe-cube::after {
            content: '';
            position: absolute;
            width: 60px;
            height: 60px;
            border: 1px solid #00ffff;
          }

          .wireframe-cube::before {
            transform: translateZ(30px);
          }

          .wireframe-cube::after {
            transform: translateZ(-30px);
          }

          @keyframes wireframeRotate {
            0% { transform: rotateX(20deg) rotateY(20deg); }
            100% { transform: rotateX(20deg) rotateY(380deg); }
          }

          .model-name {
            color: #00ffff;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
          }

          .model-details {
            color: #b0c4de;
            font-size: 10px;
            text-align: center;
          }

          /* CSS Editor Styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid rgba(0, 255, 255, 0.3);
            background: rgba(15, 25, 40, 0.9);
            color: #00ffff;
            resize: vertical;
            backdrop-filter: blur(10px);
          }

          .css-editor {
            margin: auto;
            max-width: 800px;
            min-width: 500px;
            min-height: 150px;
            text-align: left;
            display: block;
          }

          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: left;
            display: block;
          }

          /* Timeline Styles */
          .era-timeline {
            background: rgba(15, 25, 40, 0.9) !important;
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 12px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          }

          .mermaid {
            background: rgba(15, 25, 40, 0.95) !important;
            color: #e0e6ed !important;
            border-radius: 8px;
          }

          /* Show WebGL elements when theme is active */
          body.webgl-3d-active #webgl-3d-elements {
            display: block !important;
          }

          body.webgl-3d-active .floating-3d-panels,
          body.webgl-3d-active .particle-field,
          body.webgl-3d-active .nav-cube-container,
          body.webgl-3d-active .holographic-ui,
          body.webgl-3d-active .model-showcase {
            display: block !important;
          }
        `,
        'darkCss': `
          /* WebGL & 3D Theme - Dark Mode */
          body {
            font-family: 'Orbitron', 'Arial', monospace;
            background: linear-gradient(135deg,
              #000000 0%,
              #0d1421 25%,
              #1a1a2e 50%,
              #16213e 75%,
              #0f3460 100%);
            color: #ffffff;
            overflow-x: hidden;
            perspective: 1000px;
            min-height: 100vh;
          }

          /* 3D Scene Container - Dark Mode */
          .webgl-scene-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: -2;
            background: radial-gradient(ellipse at center,
              rgba(15, 52, 96, 0.4) 0%,
              rgba(10, 10, 10, 0.7) 50%,
              rgba(0, 0, 0, 0.9) 100%);
          }

          /* HUD Elements - Dark Mode */
          .hud-element {
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #ff6b35;
            color: #ff6b35;
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
          }

          .hud-title {
            color: #ffffff;
          }

          .stat-line, .control-hint {
            color: #ff6b35;
          }

          /* Main Content - Dark Mode */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 10;
          }

          /* 3D Style Cards - Dark Mode */
          .style-card {
            background: linear-gradient(145deg,
              rgba(10, 10, 10, 0.95),
              rgba(20, 20, 20, 0.95));
            border: 2px solid rgba(255, 107, 53, 0.6);
            border-radius: 12px;
            box-shadow:
              0 8px 32px rgba(0, 0, 0, 0.5),
              0 0 0 1px rgba(255, 255, 255, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: perspective(1000px) rotateX(5deg) rotateY(-5deg);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
          }

          .style-card::before {
            background: linear-gradient(90deg,
              transparent,
              rgba(255, 107, 53, 0.3),
              transparent);
          }

          .style-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(20px);
            box-shadow:
              0 20px 40px rgba(255, 107, 53, 0.4),
              0 0 0 1px rgba(255, 107, 53, 0.7),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 107, 53, 1);
          }

          .style-title {
            color: #ff6b35;
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.6);
          }

          .style-description {
            color: #cccccc;
          }

          .style-links a {
            color: #ffaa80;
          }

          .style-links a:hover {
            color: #ff6b35;
            border-bottom-color: #ff6b35;
            text-shadow: 0 0 5px rgba(255, 107, 53, 0.6);
          }

          /* Floating 3D Panels - Dark Mode */
          .panel-3d {
            background: linear-gradient(145deg,
              rgba(5, 5, 5, 0.98),
              rgba(15, 15, 15, 0.98));
            border: 1px solid rgba(255, 107, 53, 0.5);
          }

          .panel-3d:hover {
            border-color: rgba(255, 107, 53, 0.9);
            box-shadow:
              0 12px 40px rgba(255, 107, 53, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
          }

          .panel-header {
            color: #ff6b35;
          }

          .geo-btn, .mat-btn {
            background: linear-gradient(145deg,
              rgba(255, 107, 53, 0.1),
              rgba(255, 107, 53, 0.05));
            border: 1px solid rgba(255, 107, 53, 0.4);
            color: #ff6b35;
          }

          .geo-btn:hover, .mat-btn:hover {
            background: linear-gradient(145deg,
              rgba(255, 107, 53, 0.3),
              rgba(255, 107, 53, 0.2));
            box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
          }

          .geo-btn.active, .mat-btn.active {
            background: linear-gradient(145deg,
              rgba(255, 107, 53, 0.5),
              rgba(255, 107, 53, 0.4));
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.7);
          }

          /* Slider - Dark Mode */
          .slider {
            background: rgba(255, 107, 53, 0.3);
          }

          .slider::-webkit-slider-thumb {
            background: #ff6b35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.6);
          }

          .slider::-moz-range-thumb {
            background: #ff6b35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.6);
          }

          /* Navigation Cube - Dark Mode */
          .cube-face {
            background: rgba(255, 107, 53, 0.1);
            border: 1px solid rgba(255, 107, 53, 0.6);
            color: #ff6b35;
          }

          /* Holographic UI - Dark Mode */
          .holo-ring {
            border-color: rgba(255, 107, 53, 0.4);
          }

          .holo-grid {
            background-image:
              linear-gradient(rgba(255, 107, 53, 0.15) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.15) 1px, transparent 1px);
          }

          /* Model Showcase - Dark Mode */
          .model-viewer {
            background: rgba(5, 5, 5, 0.95);
            border: 1px solid rgba(255, 107, 53, 0.5);
          }

          .wireframe-cube {
            border-color: #ff6b35;
          }

          .wireframe-cube::before,
          .wireframe-cube::after {
            border-color: #ff6b35;
          }

          .model-name {
            color: #ff6b35;
          }

          /* CSS Editor - Dark Mode */
          #css-editor,
          #html-editor {
            border: 1px solid rgba(255, 107, 53, 0.4);
            background: rgba(5, 5, 5, 0.95);
            color: #ff6b35;
          }

          /* Timeline - Dark Mode */
          .era-timeline {
            background: rgba(5, 5, 5, 0.95) !important;
            border: 1px solid rgba(255, 107, 53, 0.4);
          }

          .mermaid {
            background: rgba(5, 5, 5, 0.98) !important;
            color: #ffffff !important;
          }

          /* Show WebGL elements when theme is active - Dark Mode */
          body.webgl-3d-active #webgl-3d-elements {
            display: block !important;
          }

          body.webgl-3d-active .floating-3d-panels,
          body.webgl-3d-active .particle-field,
          body.webgl-3d-active .nav-cube-container,
          body.webgl-3d-active .holographic-ui,
          body.webgl-3d-active .model-showcase {
            display: block !important;
          }
      `
      },
      {
        'id': 'inclusive-design',
        'handle': 'inclusive-designStyle',
        'name': 'Inclusive Design',
        'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Arial', sans-serif;
          background: #ffde59;
          color: black;
        }
        /* Ensure main content scrolls above video */
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: white;
          border: 3px solid black;
          border-radius: 0;
          box-shadow: 5px 5px 0 black;
          transform: rotate(-1deg);
          transition: all 0.2s ease;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
  }];

  // Create Dark Themes Array from existing themes
  const darkThemes = lightThemes.map(theme => ({
    id: theme.id,
    handle: theme.handle,
    name: theme.name,
    lightCss: theme.darkCss, // Use darkCss as lightCss for dark themes
    darkCss: theme.darkCss   // Keep darkCss the same
  }));

  // Define separate video sources for light and dark modes
  const videoSources = [
    "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4",
    "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4"
  ];

  const videoSources2 = [
    "https://mytech.today/wp-content/uploads/2025/06/dark-cinematic-bg-01.mp4",
    "https://mytech.today/wp-content/uploads/2025/06/dark-cinematic-bg-02.mp4"
  ];

  // Define isDarkMode in the global scope
  let isDarkMode = localStorage.getItem('darkMode') === 'true';

  // Function to get the appropriate video sources based on dark mode
  function getVideoSources() {
    return isDarkMode ? videoSources2 : videoSources;
  }

  // Refactored Theme Switcher with Separate Light and Dark Arrays
  jQuery(function($) {
    try {
      const videoSources = [
        "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4",
        "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4"
      ];

      const videoSources2 = [
        "https://mytech.today/wp-content/uploads/2025/04/tTrucks.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/Notgridlock.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/Gridlock.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/RTA_bus-2-12043240-3840-2160-24Fps.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/lsd-01-12057787-3840-2160-24Fps.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/lsd-02-.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/lower-waker-bridge-2-2005974-Hd-1920-1080-24Fps.mp4"
      ];

      const $themeStyle = $('#theme-style');
      const $cssEditor = $('#css-editor');
      const $heroSection = $('.hero-section');
      const $contentWrapper = $('.content-wrapper');
      const $video1 = $('#color_videos_1');
      const $video2 = $('#color_videos_2');
      const $underConstruction = $('#under-construction');
      const $visitorCounter = $('.counter');

      let current = 0;
      const themes = getCurrentThemeSet();
      
      // Single video handling function
      function handleCinematicVideos(action) {
        if (action === 'start') {
          $heroSection.show();
          $contentWrapper.show();
          
          // Get the appropriate video sources based on dark mode
          const currentSources = getVideoSources();
          
          $video1.attr('src', currentSources[0]);
          $video2.attr('src', currentSources[1]);
          
          // Rest of your video handling code
          if (!$video1.attr('src')) {
            $video1.attr('src', videoSources[0]);
            $video2.attr('src', videoSources[1]);
            
            // Wait for videos to load before starting
            let loadedVideos = 0;
            const totalVideos = 2;
            
            const checkAndStart = () => {
              if (loadedVideos === totalVideos) {
                startVideoPlayback();
              }
            };
            
            $video1.on('loadeddata', () => {
              console.log('Video 1 loaded');
              loadedVideos++;
              checkAndStart();
            }).on('error', e => {
              console.error('Error loading video 1:', e);
            });
            
            $video2.on('loadeddata', () => {
              console.log('Video 2 loaded');
              loadedVideos++;
              checkAndStart();
            }).on('error', e => {
              console.error('Error loading video 2:', e);
            });
          } else {
            // Videos already loaded, just start playback
            startVideoPlayback();
          }
        } else if (action === 'stop') {
          $heroSection.hide();
          if ($video1[0]) $video1[0].pause();
          if ($video2[0]) $video2[0].pause();
        }
      }
      
      function startVideoPlayback() {
        $video1.css('opacity', '1');
        $video2.css('opacity', '0');
        $video1.css('display', 'block');
        
        // Clear previous event handlers to prevent duplicates
        $video1.off('ended');
        $video2.off('ended');
        
        $video1[0].play().catch(e => console.error('Play error video1:', e));
        
        $video1.on('ended', () => {
          $video1.css('opacity', '0');
          $video2.css('opacity', '1');
          $video2.css('display', 'block');
          $video2[0].play().catch(e => console.error('Play error video2:', e));
        });
        
        $video2.on('ended', () => {
          $video2.css('opacity', '0');
          $video1.css('opacity', '1');
          $video1[0].currentTime = 0;
          $video1[0].play().catch(e => console.error('Replay video1 failed:', e));
        });
      }

      // === Theme State Initialization ===
      let isDarkMode = localStorage.getItem('darkMode') === 'true';


      function formatCss(css) {
        try {
          if (!css) return '/* No CSS defined */';
          return css
            .replace(/}/g, '};\n')
            .replace(/{/g, ' {\n')
            .replace(/;(?!\n)/g, ';\n')
            .replace(/:(?! )/g, ': ')
            .trim();
        } catch (e) {
          console.error('CSS formatting error:', e);
          return '/* CSS formatting error */';
        }
      }

      function dropTheme() {
        $('body').removeClass(function(i, c) {
          return (c.match(/(^|\s)theme-\S+/g) || []).join(' ');
        });
      }

      function getCurrentThemeSet() {
        const isDark = $('#dark-mode-toggle').is(':checked');
        return isDark ? darkThemes : lightThemes;
      }

      // === Apply Theme by Index ===
      function applyTheme(index) {
        const themeSet = getCurrentThemeSet();
        const theme = themeSet[index];
        if (!theme) {
          console.error(`Invalid theme index: ${index}`);
          return;
        }

        const cssVars = isDarkMode ? theme.darkCss : theme.lightCss;
        if (!cssVars) {
          console.error(`Missing ${isDarkMode ? 'dark' : 'light'} CSS for theme "${theme.name}"`);
          return;
        }

        const prettyCss = prettier.format(cssVars, {
          parser: 'css',
          plugins: prettierPlugins,
          printWidth: 80,
          tabWidth: 2,
          useTabs: false
        });

        $themeStyle.html(prettyCss);
        $cssEditor.val(formatCss(prettyCss));

        // Update the current theme display
        $('#current-theme').text(theme.name);

        Object.entries(prettyCss).forEach(([key, value]) => {
          document.documentElement.style.setProperty(key, value);
        });

        // Dispatch theme changed event
        document.dispatchEvent(new CustomEvent('themeChanged', {
          detail: { themeName: theme.name, isDarkMode: isDarkMode }
        }));

        console.log(`Applied theme "${theme.name}" (index: ${index}) in ${isDarkMode ? 'Dark' : 'Light'} Mode`);
        localStorage.setItem('lastThemeIndex', index);

        // Handle cinematic mode
        if (theme.name.includes('Cinematic')) {
          handleCinematicVideos('start');
        } else {
          handleCinematicVideos('stop');
        }

        if (theme.name.includes('Vernacular')) {
          $underConstruction.show();
          $visitorCounter.show();
        } else {
          $underConstruction.hide();
          $visitorCounter.hide();
        }

        // Handle retro web theme special effects
        if (theme.name.includes('Retro Web')) {
          initRetroEffects();
        } else {
          cleanupRetroEffects();
        }

        // Handle Y2K web theme special effects
        if (theme.name.includes('Y2K Web')) {
          initY2KEffects();
        } else {
          cleanupY2KEffects();
        }

        // Handle WebGL & 3D theme special effects
        if (theme.name.includes('WebGL & 3D')) {
          setTimeout(() => {
            if (typeof initWebGL3DEffects === 'function') {
              initWebGL3DEffects();
            }
          }, 100);
        } else {
          if (typeof cleanupWebGL3DEffects === 'function') {
            cleanupWebGL3DEffects();
          }
        }
      }

      // === Dark Mode Toggle Handler ===
      $('#dark-mode-toggle').on('change', function() {
        isDarkMode = this.checked;
        localStorage.setItem('darkMode', isDarkMode);

        // Reapply the current theme with the new dark/light mode setting
        const lastIndex = parseInt(localStorage.getItem('lastThemeIndex'), 10) || 0;
        applyTheme(lastIndex);
        
        console.log(`Dark Mode toggled: ${isDarkMode}`);
      });

      // === Load Theme from Local Storage on Page Load ===
      $(document).ready(function() {
        const lastIndex = parseInt(localStorage.getItem('lastThemeIndex'), 10);
        if (!isNaN(lastIndex)) {
          applyTheme(lastIndex);
        }

        $('#dark-mode-toggle').prop('checked', isDarkMode);
        
        // Bind all click handlers
        rebindClickHandlers();
      });

      // Function to rebind all click handlers
      function rebindClickHandlers() {
        // Remove existing handlers first
        $('.style-card').off('click');
        $('.style-title').off('click');
        $('.mermaid .task, .mermaid text.taskText').off('click');
        
        // Rebind style card clicks
        $('.style-card').on('click', function () {
          const index = $(this).data('index');
          if (typeof index === 'number') {
            console.log(`Style card clicked. Applying index: ${index}`);
            applyTheme(index);
            current = index;
            gtag('event', 'apply_theme_click', {
              event_category: 'Theme Card Click',
              event_label: getCurrentThemeSet()[index].handle,
              value: index
            });
          }
        });
        
        // Rebind style title clicks
        $('.style-title').on('click', function (e) {
          e.stopPropagation();
          const $card = $(this).closest('.style-card');
          const index = $card.data('index');
          if (typeof index === 'number') {
            console.log(`Style title clicked. Applying index: ${index}`);
            applyTheme(index);
            current = index;
            gtag('event', 'apply_theme_click', {
              event_category: 'Theme Title Click',
              event_label: getCurrentThemeSet()[index].handle,
              value: index
            });
          }
        });

        // Rebind Mermaid timeline task clicks
        $('.mermaid .task, .mermaid text.taskText').on('click', function () {
          const elementId = this.id;
          if (elementId) {
            // Find the theme with matching ID and apply it
            const themeSet = getCurrentThemeSet();
            const themeIndex = themeSet.findIndex(theme => theme.id === elementId);
            if (themeIndex !== -1) {
              applyTheme(themeIndex);
              current = themeIndex;
              console.log(`Applied theme by ID: ${elementId}, index: ${themeIndex}`);
            } else {
              console.warn(`No theme found with ID: ${elementId}`);
            }
          } else {
            console.warn('Clicked Mermaid element has no ID');
          }
        });
      }

      // CSS editor live updates
      $cssEditor.on('input', function () {
        const customCss = $(this).val();
        $themeStyle.html(customCss);
        console.log('Custom CSS applied from editor');
      });

      // Keyboard shortcut: Ctrl + Shift + Space to cycle styles
      $(document).on('keydown.themeCycle', function (e) {
        if (e.ctrlKey && e.shiftKey && e.keyCode === 32) {
          const themes = getCurrentThemeSet();
          const nextIndex = (current + 1) % themes.length;
          applyTheme(nextIndex);
          e.preventDefault();
        }
      });
    } catch (e) {
      console.error('Error with script', e);
    }

    // Retro Web Effects Functions
    let matrixInterval;
    let statusInterval;

    function initRetroEffects() {
      createMatrixRain();
      animateStatusBar();
      addRetroSounds();
    }

    function cleanupRetroEffects() {
      if (matrixInterval) clearInterval(matrixInterval);
      if (statusInterval) clearInterval(statusInterval);
      $('#matrix-rain').empty();
    }

    function createMatrixRain() {
      const matrixContainer = $('#matrix-rain');
      matrixContainer.empty();

      const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
      const columns = Math.floor(window.innerWidth / 20);

      for (let i = 0; i < columns; i++) {
        const column = $('<div>').css({
          position: 'absolute',
          left: i * 20 + 'px',
          top: '-100px',
          color: '#00ff41',
          fontSize: '14px',
          fontFamily: 'monospace',
          opacity: 0.7,
          animation: `matrixFall ${Math.random() * 3 + 2}s linear infinite`,
          animationDelay: Math.random() * 2 + 's'
        });

        let text = '';
        for (let j = 0; j < 20; j++) {
          text += chars[Math.floor(Math.random() * chars.length)] + '<br>';
        }
        column.html(text);
        matrixContainer.append(column);
      }

      // Add CSS animation for matrix fall
      if (!$('#matrix-animation').length) {
        $('<style id="matrix-animation">').text(`
          @keyframes matrixFall {
            0% { transform: translateY(-100vh); opacity: 0; }
            10% { opacity: 0.7; }
            90% { opacity: 0.7; }
            100% { transform: translateY(100vh); opacity: 0; }
          }
        `).appendTo('head');
      }
    }

    function animateStatusBar() {
      const messages = [
        'Welcome to my homepage! Loading awesome content...',
        'Connecting to the information superhighway...',
        'Best viewed with Netscape Navigator 4.0+',
        'This site is Y2K compliant!',
        'Made with love and HTML 3.2',
        'Optimized for 56k modem',
        'Member of 42 webrings',
        'Last updated: Never'
      ];

      let messageIndex = 0;
      statusInterval = setInterval(() => {
        $('#status-text').text(messages[messageIndex]);
        messageIndex = (messageIndex + 1) % messages.length;
      }, 3000);
    }

    function addRetroSounds() {
      // Add click sounds to retro elements (optional)
      $('.retro-blink, .webring-link, .nav-link').on('click', function(e) {
        e.preventDefault();
        // Create a brief audio context beep (Web Audio API)
        try {
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.1);
        } catch (e) {
          // Fallback for browsers without Web Audio API
          console.log('Beep!');
        }
      });
    }

    // Y2K Web Effects Functions
    let y2kParticleInterval;
    let y2kCursorTrail = [];
    let y2kCountdownInterval;

    function initY2KEffects() {
      $('#y2k-elements').show();
      createY2KParticles();
      initY2KCursorTrail();
      startY2KCountdown();
      addY2KInteractivity();
    }

    function cleanupY2KEffects() {
      $('#y2k-elements').hide();
      if (y2kParticleInterval) clearInterval(y2kParticleInterval);
      if (y2kCountdownInterval) clearInterval(y2kCountdownInterval);
      $(document).off('mousemove.y2k');
      $('#particle-system').empty();
    }

    function createY2KParticles() {
      const particleContainer = $('#particle-system');
      particleContainer.empty();

      // Create floating particles
      for (let i = 0; i < 20; i++) {
        const particle = $('<div>').css({
          position: 'absolute',
          width: Math.random() * 6 + 2 + 'px',
          height: Math.random() * 6 + 2 + 'px',
          background: `linear-gradient(45deg,
            hsl(${Math.random() * 360}, 70%, 60%),
            hsl(${Math.random() * 360}, 70%, 80%))`,
          borderRadius: '50%',
          left: Math.random() * 100 + '%',
          top: Math.random() * 100 + '%',
          opacity: Math.random() * 0.7 + 0.3,
          animation: `y2kParticleFloat ${Math.random() * 10 + 5}s ease-in-out infinite`,
          animationDelay: Math.random() * 5 + 's',
          boxShadow: '0 0 10px rgba(255,255,255,0.5)'
        });
        particleContainer.append(particle);
      }

      // Add CSS animation for particles
      if (!$('#y2k-particle-animation').length) {
        $('<style id="y2k-particle-animation">').text(`
          @keyframes y2kParticleFloat {
            0%, 100% {
              transform: translateY(0px) translateX(0px) scale(1);
              opacity: 0.3;
            }
            25% {
              transform: translateY(-30px) translateX(20px) scale(1.2);
              opacity: 0.7;
            }
            50% {
              transform: translateY(20px) translateX(-15px) scale(0.8);
              opacity: 0.4;
            }
            75% {
              transform: translateY(-10px) translateX(30px) scale(1.1);
              opacity: 0.6;
            }
          }
        `).appendTo('head');
      }
    }

    function initY2KCursorTrail() {
      $(document).on('mousemove.y2k', function(e) {
        const trail = $('#cursor-trail');
        trail.css({
          left: e.pageX - 10 + 'px',
          top: e.pageY - 10 + 'px',
          opacity: 1
        });

        // Create trailing particles
        if (Math.random() > 0.7) {
          const trailParticle = $('<div>').css({
            position: 'absolute',
            left: e.pageX + Math.random() * 20 - 10 + 'px',
            top: e.pageY + Math.random() * 20 - 10 + 'px',
            width: '4px',
            height: '4px',
            background: 'linear-gradient(45deg, #ff006e, #8338ec)',
            borderRadius: '50%',
            pointerEvents: 'none',
            zIndex: 1000,
            animation: 'trailFade 1s ease-out forwards'
          });

          $('body').append(trailParticle);
          setTimeout(() => trailParticle.remove(), 1000);
        }
      });

      // Add trail fade animation
      if (!$('#y2k-trail-animation').length) {
        $('<style id="y2k-trail-animation">').text(`
          @keyframes trailFade {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0); }
          }
        `).appendTo('head');
      }
    }

    function startY2KCountdown() {
      // Simulate a countdown to Y2K (just for effect)
      let days = Math.floor(Math.random() * 365);
      let hours = Math.floor(Math.random() * 24);
      let minutes = Math.floor(Math.random() * 60);

      y2kCountdownInterval = setInterval(() => {
        minutes--;
        if (minutes < 0) {
          minutes = 59;
          hours--;
          if (hours < 0) {
            hours = 23;
            days--;
            if (days < 0) {
              days = 365;
            }
          }
        }

        $('.countdown-number').eq(0).text(String(days).padStart(2, '0'));
        $('.countdown-number').eq(1).text(String(hours).padStart(2, '0'));
        $('.countdown-number').eq(2).text(String(minutes).padStart(2, '0'));
      }, 1000);
    }

    function addY2KInteractivity() {
      // Make UI panels interactive
      $('.ui-panel').on('click', function() {
        $(this).css('animation-play-state', 'paused');
        setTimeout(() => {
          $(this).css('animation-play-state', 'running');
        }, 2000);
      });

      // Chrome orb interactions
      $('.chrome-orb').on('click', function() {
        $(this).css({
          animation: 'none',
          transform: 'scale(1.5)',
          transition: 'all 0.3s ease'
        });

        setTimeout(() => {
          $(this).css({
            animation: '',
            transform: '',
            transition: ''
          });
        }, 500);
      });
    }

    // Initialize Y2K effects if Y2K theme is already active
    $(document).ready(function() {
      const currentTheme = $('#current-theme').text();
      if (currentTheme.includes('Y2K Web')) {
        initY2KEffects();
      }
    });

    // Initialize retro effects if retro theme is already active
    $(document).ready(function() {
      const currentTheme = $('#current-theme').text();
      if (currentTheme.includes('Retro Web')) {
        initRetroEffects();
      }
    });

    // ===== WebGL & 3D Theme JavaScript =====
    let webglScene, webglRenderer, webglCamera, webglMesh;
    let webglAnimationId;

    function initWebGL3DEffects() {
      console.log('Initializing WebGL & 3D effects...');

      // Add body class for CSS targeting
      document.body.classList.add('webgl-3d-active');

      // Initialize Three.js scene if available
      if (typeof THREE !== 'undefined') {
        initThreeJSScene();
        animateWebGL();
      }

      // Initialize particle system
      initParticleSystem();

      // Initialize interactive controls
      initWebGLControls();

      // Update HUD stats
      updateHUDStats();
    }

    function initThreeJSScene() {
      const canvas = document.getElementById('webgl-canvas');
      if (!canvas) return;

      try {
        // Scene setup
        webglScene = new THREE.Scene();
        webglCamera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        webglRenderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });
        webglRenderer.setSize(window.innerWidth, window.innerHeight);
        webglRenderer.setClearColor(0x000000, 0);

        // Create geometry
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshPhongMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.8,
          wireframe: false
        });
        webglMesh = new THREE.Mesh(geometry, material);
        webglScene.add(webglMesh);

        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        webglScene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0x00ffff, 0.7);
        directionalLight.position.set(5, 5, 5);
        webglScene.add(directionalLight);

        // Position camera
        webglCamera.position.z = 5;

        // Handle window resize
        window.addEventListener('resize', onWebGLWindowResize, false);
      } catch (error) {
        console.error('Error initializing Three.js scene:', error);
      }
    }

    function initParticleSystem() {
      const particleField = document.getElementById('particle-field-3d');
      if (!particleField) return;

      // Clear existing particles
      particleField.innerHTML = '';

      // Create floating particles
      for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle-3d';
        particle.style.cssText = `
          position: absolute;
          width: 2px;
          height: 2px;
          background: #00ffff;
          border-radius: 50%;
          box-shadow: 0 0 6px #00ffff;
          left: ${Math.random() * 100}%;
          top: ${Math.random() * 100}%;
          animation: particleFloat ${3 + Math.random() * 4}s infinite ease-in-out;
          animation-delay: ${Math.random() * 2}s;
        `;
        particleField.appendChild(particle);
      }

      // Add particle animation CSS if not already added
      if (!document.getElementById('webgl-particle-styles')) {
        const style = document.createElement('style');
        style.id = 'webgl-particle-styles';
        style.textContent = `
          @keyframes particleFloat {
            0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
            25% { transform: translateY(-20px) translateX(10px); opacity: 1; }
            50% { transform: translateY(-10px) translateX(-15px); opacity: 0.7; }
            75% { transform: translateY(-30px) translateX(5px); opacity: 1; }
          }
        `;
        document.head.appendChild(style);
      }
    }

    function initWebGLControls() {
      // Geometry controls
      $('.geo-btn').off('click.webgl').on('click.webgl', function() {
        $('.geo-btn').removeClass('active');
        $(this).addClass('active');

        const shape = $(this).data('shape');
        changeGeometry(shape);
      });

      // Material controls
      $('.mat-btn').off('click.webgl').on('click.webgl', function() {
        $('.mat-btn').removeClass('active');
        $(this).addClass('active');

        const material = $(this).data('material');
        changeMaterial(material);
      });

      // Lighting controls
      $('#ambient-light').off('input.webgl').on('input.webgl', function() {
        const value = $(this).val() / 100;
        if (webglScene) {
          const ambientLight = webglScene.children.find(child => child.type === 'AmbientLight');
          if (ambientLight) ambientLight.intensity = value;
        }
      });

      $('#directional-light').off('input.webgl').on('input.webgl', function() {
        const value = $(this).val() / 100;
        if (webglScene) {
          const directionalLight = webglScene.children.find(child => child.type === 'DirectionalLight');
          if (directionalLight) directionalLight.intensity = value;
        }
      });
    }

    function changeGeometry(shape) {
      if (!webglMesh || typeof THREE === 'undefined') return;

      let geometry;
      try {
        switch(shape) {
          case 'sphere':
            geometry = new THREE.SphereGeometry(1.5, 32, 32);
            break;
          case 'torus':
            geometry = new THREE.TorusGeometry(1, 0.4, 16, 100);
            break;
          case 'dodecahedron':
            geometry = new THREE.DodecahedronGeometry(1.5);
            break;
          default:
            geometry = new THREE.BoxGeometry(2, 2, 2);
        }

        webglMesh.geometry.dispose();
        webglMesh.geometry = geometry;

        // Update vertex counter
        $('#vertex-counter').text(geometry.attributes.position.count.toLocaleString());
        $('#triangle-counter').text((geometry.attributes.position.count / 3).toFixed(0));
      } catch (error) {
        console.error('Error changing geometry:', error);
      }
    }

    function changeMaterial(materialType) {
      if (!webglMesh || typeof THREE === 'undefined') return;

      let material;
      try {
        switch(materialType) {
          case 'glass':
            material = new THREE.MeshPhongMaterial({
              color: 0x88ccff,
              transparent: true,
              opacity: 0.3,
              wireframe: false
            });
            break;
          case 'neon':
            material = new THREE.MeshBasicMaterial({
              color: 0xff00ff,
              wireframe: true
            });
            break;
          case 'hologram':
            material = new THREE.MeshPhongMaterial({
              color: 0x00ffff,
              transparent: true,
              opacity: 0.6,
              wireframe: true
            });
            break;
          default:
            material = new THREE.MeshPhongMaterial({
              color: 0x00ffff,
              transparent: true,
              opacity: 0.8,
              wireframe: false
            });
        }

        webglMesh.material.dispose();
        webglMesh.material = material;
      } catch (error) {
        console.error('Error changing material:', error);
      }
    }

    function animateWebGL() {
      if (!document.body.classList.contains('webgl-3d-active')) return;

      webglAnimationId = requestAnimationFrame(animateWebGL);

      if (webglMesh) {
        webglMesh.rotation.x += 0.01;
        webglMesh.rotation.y += 0.01;
      }

      if (webglRenderer && webglScene && webglCamera) {
        webglRenderer.render(webglScene, webglCamera);
      }
    }

    function updateHUDStats() {
      let fps = 60;
      let lastTime = performance.now();

      function calculateFPS() {
        const now = performance.now();
        fps = Math.round(1000 / (now - lastTime));
        lastTime = now;

        $('#fps-counter').text(fps);

        if (document.body.classList.contains('webgl-3d-active')) {
          setTimeout(calculateFPS, 100);
        }
      }

      calculateFPS();
    }

    function onWebGLWindowResize() {
      if (webglCamera && webglRenderer) {
        webglCamera.aspect = window.innerWidth / window.innerHeight;
        webglCamera.updateProjectionMatrix();
        webglRenderer.setSize(window.innerWidth, window.innerHeight);
      }
    }

    function cleanupWebGL3DEffects() {
      console.log('Cleaning up WebGL & 3D effects...');

      // Remove body class
      document.body.classList.remove('webgl-3d-active');

      // Stop animation
      if (webglAnimationId) {
        cancelAnimationFrame(webglAnimationId);
        webglAnimationId = null;
      }

      // Cleanup Three.js resources
      if (webglRenderer) {
        webglRenderer.dispose();
        webglRenderer = null;
      }

      if (webglMesh && webglMesh.geometry && webglMesh.material) {
        webglMesh.geometry.dispose();
        webglMesh.material.dispose();
        webglMesh = null;
      }

      webglScene = null;
      webglCamera = null;

      // Clear particle field
      const particleField = document.getElementById('particle-field-3d');
      if (particleField) {
        particleField.innerHTML = '';
      }

      // Remove event listeners
      $('.geo-btn, .mat-btn').off('click.webgl');
      $('#ambient-light, #directional-light').off('input.webgl');
      window.removeEventListener('resize', onWebGLWindowResize);
    }

    // Add WebGL theme handling to the existing applyTheme function
    const originalApplyTheme = window.applyTheme;
    if (originalApplyTheme) {
      window.applyTheme = function(index) {
        // Call original function
        originalApplyTheme(index);

        // Get current theme
        const themeSet = getCurrentThemeSet();
        const theme = themeSet[index];

        if (theme && theme.name.includes('WebGL & 3D')) {
          setTimeout(initWebGL3DEffects, 100);
        } else {
          cleanupWebGL3DEffects();
        }
      };
    }

    // Initialize WebGL effects if WebGL theme is already active
    $(document).ready(function() {
      setTimeout(() => {
        const currentTheme = $('#current-theme').text();
        if (currentTheme.includes('WebGL & 3D')) {
          initWebGL3DEffects();
        }
      }, 500);
    });

  });
  </script>
  </body>
</html>
